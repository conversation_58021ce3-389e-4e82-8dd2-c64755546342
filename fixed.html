<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Block Battle Arena - 修復版</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            font-family: 'Arial', sans-serif;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        #game-container {
            border: 2px solid #00d4ff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
            background: #000;
        }

        #ui-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1000;
        }

        .health-bar {
            position: absolute;
            top: 20px;
            width: 200px;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid #fff;
            border-radius: 10px;
            overflow: hidden;
        }

        .health-bar.player1 {
            left: 20px;
        }

        .health-bar.player2 {
            right: 20px;
        }

        .health-fill {
            height: 100%;
            transition: width 0.3s ease;
        }

        .health-fill.player1 {
            background: linear-gradient(90deg, #00d4ff, #0099cc);
        }

        .health-fill.player2 {
            background: linear-gradient(90deg, #ff6b35, #cc5529);
        }

        .health-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            font-size: 12px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .skill-bar {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
            pointer-events: auto;
        }

        .skill-button {
            width: 50px;
            height: 50px;
            border: 2px solid #fff;
            border-radius: 8px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            font-weight: bold;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .skill-button:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: scale(1.05);
        }

        .skill-button.cooldown {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .skill-button .cooldown-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            background: rgba(255, 0, 0, 0.6);
            transition: height 0.1s linear;
        }

        .mode-indicator {
            position: absolute;
            top: 60px;
            left: 20px;
            padding: 10px 15px;
            border-radius: 20px;
            color: white;
            font-weight: bold;
            text-transform: uppercase;
            border: 2px solid;
            background: rgba(0, 0, 0, 0.7);
        }

        .mode-indicator.attack {
            border-color: #ff6b35;
            color: #ff6b35;
        }

        .mode-indicator.defense {
            border-color: #00d4ff;
            color: #00d4ff;
        }

        .mode-indicator.neutral {
            border-color: #ffffff;
            color: #ffffff;
        }

        .game-timer {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }

        .loading-screen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 2000;
        }

        .loading-text {
            font-size: 24px;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .controls-info {
            position: absolute;
            bottom: 20px;
            right: 20px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
            text-align: right;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div id="game-container">
        <div id="ui-overlay">
            <!-- 血量條 -->
            <div class="health-bar player1">
                <div class="health-fill player1" style="width: 100%;"></div>
                <div class="health-text">100/100</div>
            </div>
            <div class="health-bar player2">
                <div class="health-fill player2" style="width: 100%;"></div>
                <div class="health-text">100/100</div>
            </div>

            <!-- 遊戲計時器 -->
            <div class="game-timer">00:00</div>

            <!-- 模式指示器 -->
            <div class="mode-indicator neutral">中性模式</div>

            <!-- 技能欄 -->
            <div class="skill-bar">
                <div class="skill-button" data-skill="shockwave" title="衝擊波 (Q)">
                    Q
                    <div class="cooldown-overlay" style="height: 0%;"></div>
                </div>
                <div class="skill-button" data-skill="shield" title="能量護盾 (W)">
                    W
                    <div class="cooldown-overlay" style="height: 0%;"></div>
                </div>
                <div class="skill-button" data-skill="teleport" title="瞬移 (E)">
                    E
                    <div class="cooldown-overlay" style="height: 0%;"></div>
                </div>
                <div class="skill-button" data-skill="boost" title="瞬間加速 (R)">
                    R
                    <div class="cooldown-overlay" style="height: 0%;"></div>
                </div>
            </div>

            <!-- 控制說明 -->
            <div class="controls-info">
                <div><strong>控制說明:</strong></div>
                <div>空白鍵: 切換模式</div>
                <div>Q/W/E/R: 技能</div>
                <div>滑鼠: 瞄準方向</div>
            </div>
        </div>

        <!-- 載入畫面 -->
        <div class="loading-screen" id="loading-screen">
            <div class="loading-text">載入中...</div>
            <div>Block Battle Arena - 修復版</div>
        </div>
    </div>

    <!-- 遊戲腳本 -->
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/matter-js@0.19.0/build/matter.min.js"></script>

    <!-- 只載入基本腳本，不包含可能有問題的擴展 -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/entities/Player.js"></script>
    <script src="js/entities/Skill.js"></script>
    <script src="js/managers/SkillManager.js"></script>
    <script src="js/managers/UIManager.js"></script>
    <script src="js/scenes/MenuScene.js"></script>
    <script src="js/scenes/GameScene.js"></script>
    <script src="js/scenes/GameOverScene.js"></script>

    <script>
        // 修復版遊戲配置
        const fixedGameConfig = {
            type: Phaser.AUTO,
            width: GAME_CONFIG.SCREEN.WIDTH,
            height: GAME_CONFIG.SCREEN.HEIGHT,
            parent: 'game-container',
            backgroundColor: GAME_CONFIG.SCREEN.BACKGROUND_COLOR,

            physics: {
                default: 'matter',
                matter: {
                    gravity: GAME_CONFIG.PHYSICS.GRAVITY,
                    debug: false,
                    enableSleeping: false,  // 禁用睡眠模式
                    timing: {
                        timeScale: 1
                    }
                }
            },

            scene: [MenuScene, GameScene, GameOverScene],

            input: {
                keyboard: true,
                mouse: true,
                touch: true
            },

            render: {
                antialias: true,
                pixelArt: false,
                roundPixels: false
            }
        };

        // 錯誤處理
        window.addEventListener('error', (event) => {
            const errorMessage = event.error ? event.error.message : 'Unknown error';
            console.error('Global error:', errorMessage);

            const loadingScreen = document.querySelector('#loading-screen');
            if (loadingScreen) {
                loadingScreen.innerHTML = `
                    <div style="color: #ff6b35; font-size: 24px; margin-bottom: 20px;">載入失敗</div>
                    <div style="color: #cccccc; font-size: 16px; text-align: center;">
                        錯誤: ${errorMessage}<br><br>
                        <button onclick="location.reload()" style="
                            padding: 10px 20px;
                            background: #00d4ff;
                            color: white;
                            border: none;
                            border-radius: 5px;
                            cursor: pointer;
                        ">重新載入</button>
                    </div>
                `;
            }
        });

        // 初始化遊戲
        document.addEventListener('DOMContentLoaded', () => {
            console.log('DOM loaded, initializing fixed version...');

            try {
                // 檢查依賴
                const requiredClasses = ['GAME_CONFIG', 'Utils', 'Player', 'Skill', 'SkillManager', 'UIManager', 'MenuScene', 'GameScene', 'GameOverScene'];

                for (const className of requiredClasses) {
                    if (typeof window[className] === 'undefined') {
                        throw new Error(`Missing class: ${className}`);
                    }
                }

                console.log('All dependencies loaded successfully');

                // 創建遊戲
                const game = new Phaser.Game(fixedGameConfig);
                console.log('Fixed game initialized successfully');

                // 隱藏載入畫面
                setTimeout(() => {
                    const loadingScreen = document.querySelector('#loading-screen');
                    if (loadingScreen) {
                        loadingScreen.style.opacity = '0';
                        setTimeout(() => {
                            loadingScreen.style.display = 'none';
                        }, 500);
                    }
                }, 1500);

            } catch (error) {
                console.error('Game initialization failed:', error);

                const loadingScreen = document.querySelector('#loading-screen');
                if (loadingScreen) {
                    loadingScreen.innerHTML = `
                        <div style="color: #ff6b35; font-size: 24px; margin-bottom: 20px;">初始化失敗</div>
                        <div style="color: #cccccc; font-size: 16px; text-align: center;">
                            ${error.message}<br><br>
                            <button onclick="location.reload()" style="
                                padding: 10px 20px;
                                background: #00d4ff;
                                color: white;
                                border: none;
                                border-radius: 5px;
                                cursor: pointer;
                            ">重新載入</button>
                        </div>
                    `;
                }
            }
        });
    </script>
</body>
</html>
