<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bounce Diagnosis</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a2e;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        #game-container {
            border: 2px solid #00d4ff;
            border-radius: 10px;
            background: #000;
            width: 800px;
            height: 600px;
            margin: 20px auto;
        }
        
        .info {
            text-align: center;
            margin: 20px 0;
        }
        
        .debug {
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 11px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
        }
        
        button {
            padding: 10px 15px;
            background: #00d4ff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        button:hover {
            background: #0099cc;
        }
    </style>
</head>
<body>
    <div class="info">
        <h1>反彈診斷工具</h1>
        <p>詳細分析 Matter.js 的反彈行為</p>
    </div>
    
    <div id="game-container"></div>
    
    <div class="controls">
        <button onclick="addBall()">添加球</button>
        <button onclick="resetTest()">重置</button>
        <button onclick="toggleDebug()">切換調試</button>
        <button onclick="toggleVelocityCorrection()">切換速度修正</button>
        <button onclick="clearLog()">清除日誌</button>
    </div>
    
    <div class="debug" id="debug-info">
        載入中...
    </div>

    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/matter-js@0.19.0/build/matter.min.js"></script>
    
    <script>
        let game;
        let scene;
        let debugInfo = document.getElementById('debug-info');
        let velocityCorrection = true;
        
        function log(message) {
            console.log(message);
            const time = new Date().toLocaleTimeString();
            debugInfo.innerHTML += `[${time}] ${message}<br>`;
            debugInfo.scrollTop = debugInfo.scrollHeight;
        }
        
        function clearLog() {
            debugInfo.innerHTML = '';
        }
        
        class BounceDignosisScene extends Phaser.Scene {
            constructor() {
                super({ key: 'BounceDignosisScene' });
                this.balls = [];
                this.frameCount = 0;
                this.lastCollisionTime = 0;
            }

            create() {
                log('BounceDignosisScene: 開始創建...');
                
                // 設置物理世界
                this.matter.world.disableGravity();
                
                // 創建邊界
                this.createWalls();
                
                // 創建測試球
                this.createTestBall();
                
                // 設置碰撞監聽
                this.matter.world.on('collisionstart', (event) => {
                    this.handleCollisions(event);
                });
                
                // 添加文字
                this.add.text(400, 30, '反彈診斷工具', {
                    fontSize: '20px',
                    fill: '#ffffff',
                    fontFamily: 'Arial'
                }).setOrigin(0.5);
                
                this.statusText = this.add.text(20, 20, '狀態: 初始化', {
                    fontSize: '14px',
                    fill: '#ffffff',
                    fontFamily: 'Arial'
                });
                
                this.correctionText = this.add.text(20, 570, `速度修正: ${velocityCorrection ? '開啟' : '關閉'}`, {
                    fontSize: '14px',
                    fill: velocityCorrection ? '#00ff00' : '#ff6b35',
                    fontFamily: 'Arial'
                });
                
                log('場景創建完成');
            }

            createWalls() {
                log('創建診斷邊界...');
                
                const wallThickness = 32;
                const wallOptions = {
                    isStatic: true,
                    restitution: 1.0,
                    friction: 0,
                    frictionStatic: 0,
                    frictionAir: 0,
                    sleepThreshold: Infinity
                };
                
                // 創建四面牆並記錄詳細信息
                this.topWall = this.matter.add.rectangle(400, wallThickness/2, 800, wallThickness, wallOptions);
                this.bottomWall = this.matter.add.rectangle(400, 600 - wallThickness/2, 800, wallThickness, wallOptions);
                this.leftWall = this.matter.add.rectangle(wallThickness/2, 300, wallThickness, 600, wallOptions);
                this.rightWall = this.matter.add.rectangle(800 - wallThickness/2, 300, wallThickness, 600, wallOptions);
                
                // 標記牆壁
                this.topWall.wallType = 'top';
                this.bottomWall.wallType = 'bottom';
                this.leftWall.wallType = 'left';
                this.rightWall.wallType = 'right';
                
                // 視覺化牆壁
                this.add.rectangle(400, wallThickness/2, 800, wallThickness, 0x666666);
                this.add.rectangle(400, 600 - wallThickness/2, 800, wallThickness, 0x666666);
                this.add.rectangle(wallThickness/2, 300, wallThickness, 600, 0x666666);
                this.add.rectangle(800 - wallThickness/2, 300, wallThickness, 600, 0x666666);
                
                log('診斷邊界創建完成');
            }

            createTestBall() {
                log('創建診斷球...');
                
                const x = 400;
                const y = 300;
                const radius = 20;
                
                const ballBody = this.matter.add.circle(x, y, radius, {
                    restitution: 1.0,
                    friction: 0,
                    frictionAir: 0,
                    frictionStatic: 0,
                    density: 1,
                    inertia: Infinity,
                    sleepThreshold: Infinity
                });
                
                const ballSprite = this.add.circle(x, y, radius, 0x00d4ff);
                ballSprite.setStrokeStyle(3, 0xffffff);
                
                // 設置初始速度
                const initialVelocity = { x: 5, y: 3 };
                this.matter.body.setVelocity(ballBody, initialVelocity);
                
                const ball = {
                    body: ballBody,
                    sprite: ballSprite,
                    id: this.balls.length,
                    initialSpeed: Math.sqrt(initialVelocity.x ** 2 + initialVelocity.y ** 2),
                    collisionCount: 0,
                    lastCollisionTime: 0
                };
                
                ballBody.ballData = ball;
                this.balls.push(ball);
                
                log(`診斷球創建: ID${ball.id} 初始速度(${initialVelocity.x}, ${initialVelocity.y}) 初始速率: ${ball.initialSpeed.toFixed(2)}`);
            }

            addBall() {
                const x = 200 + Math.random() * 400;
                const y = 150 + Math.random() * 300;
                const radius = 15 + Math.random() * 10;
                const color = Math.random() * 0xffffff;
                
                const ballBody = this.matter.add.circle(x, y, radius, {
                    restitution: 1.0,
                    friction: 0,
                    frictionAir: 0,
                    frictionStatic: 0,
                    density: 1,
                    inertia: Infinity,
                    sleepThreshold: Infinity
                });
                
                const ballSprite = this.add.circle(x, y, radius, color);
                ballSprite.setStrokeStyle(2, 0xffffff);
                
                const vx = (Math.random() - 0.5) * 8;
                const vy = (Math.random() - 0.5) * 8;
                this.matter.body.setVelocity(ballBody, { x: vx, y: vy });
                
                const ball = {
                    body: ballBody,
                    sprite: ballSprite,
                    id: this.balls.length,
                    initialSpeed: Math.sqrt(vx ** 2 + vy ** 2),
                    collisionCount: 0,
                    lastCollisionTime: 0
                };
                
                ballBody.ballData = ball;
                this.balls.push(ball);
                
                log(`新球添加: ID${ball.id} 位置(${x.toFixed(1)}, ${y.toFixed(1)}) 速度(${vx.toFixed(1)}, ${vy.toFixed(1)})`);
            }

            handleCollisions(event) {
                const currentTime = this.time.now;
                
                event.pairs.forEach(pair => {
                    const bodyA = pair.bodyA;
                    const bodyB = pair.bodyB;
                    
                    let ball = null;
                    let wall = null;
                    
                    if (bodyA.ballData && bodyB.wallType) {
                        ball = bodyA.ballData;
                        wall = bodyB;
                    } else if (bodyB.ballData && bodyA.wallType) {
                        ball = bodyB.ballData;
                        wall = bodyA;
                    }
                    
                    if (ball && wall) {
                        ball.collisionCount++;
                        ball.lastCollisionTime = currentTime;
                        
                        const velocity = ball.body.velocity;
                        const speed = Math.sqrt(velocity.x ** 2 + velocity.y ** 2);
                        const speedLoss = ball.initialSpeed - speed;
                        const speedLossPercent = (speedLoss / ball.initialSpeed * 100).toFixed(1);
                        
                        log(`球${ball.id} 撞擊${wall.wallType}牆 - 碰撞#${ball.collisionCount} 速度(${velocity.x.toFixed(2)}, ${velocity.y.toFixed(2)}) 速率: ${speed.toFixed(2)} 損失: ${speedLossPercent}%`);
                        
                        // 速度修正
                        if (velocityCorrection && speed < ball.initialSpeed * 0.95) {
                            const correctionFactor = ball.initialSpeed / speed;
                            this.matter.body.setVelocity(ball.body, {
                                x: velocity.x * correctionFactor,
                                y: velocity.y * correctionFactor
                            });
                            log(`球${ball.id} 速度已修正，修正係數: ${correctionFactor.toFixed(3)}`);
                        }
                    }
                });
            }

            update() {
                this.frameCount++;
                
                // 更新所有球的視覺位置
                this.balls.forEach(ball => {
                    if (ball.body && ball.sprite) {
                        ball.sprite.setPosition(ball.body.position.x, ball.body.position.y);
                    }
                });
                
                // 每60幀更新一次狀態
                if (this.frameCount % 60 === 0 && this.balls.length > 0) {
                    const ball = this.balls[0];
                    if (ball.body) {
                        const pos = ball.body.position;
                        const vel = ball.body.velocity;
                        const speed = Math.sqrt(vel.x ** 2 + vel.y ** 2);
                        const efficiency = (speed / ball.initialSpeed * 100).toFixed(1);
                        
                        this.statusText.setText(
                            `球0: 位置(${pos.x.toFixed(1)}, ${pos.y.toFixed(1)}) ` +
                            `速度(${vel.x.toFixed(1)}, ${vel.y.toFixed(1)}) ` +
                            `效率: ${efficiency}% 碰撞: ${ball.collisionCount}`
                        );
                    }
                }
            }

            resetTest() {
                log('重置診斷測試...');
                
                this.balls.forEach(ball => {
                    if (ball.body) {
                        this.matter.world.remove(ball.body);
                    }
                    if (ball.sprite) {
                        ball.sprite.destroy();
                    }
                });
                this.balls = [];
                
                this.createTestBall();
                log('診斷測試重置完成');
            }

            toggleVelocityCorrection() {
                velocityCorrection = !velocityCorrection;
                this.correctionText.setText(`速度修正: ${velocityCorrection ? '開啟' : '關閉'}`);
                this.correctionText.setColor(velocityCorrection ? '#00ff00' : '#ff6b35');
                log(`速度修正: ${velocityCorrection ? '開啟' : '關閉'}`);
            }
        }

        // 遊戲配置
        const gameConfig = {
            type: Phaser.AUTO,
            width: 800,
            height: 600,
            parent: 'game-container',
            backgroundColor: '#1a1a2e',
            scene: [BounceDignosisScene],
            physics: {
                default: 'matter',
                matter: {
                    gravity: { x: 0, y: 0 },
                    debug: false,
                    enableSleeping: false,
                    timing: {
                        timeScale: 1
                    }
                }
            }
        };

        // 全域函數
        window.addBall = function() {
            if (scene) scene.addBall();
        };

        window.resetTest = function() {
            if (scene) scene.resetTest();
        };

        window.toggleDebug = function() {
            if (game && game.scene.scenes[0]) {
                const currentScene = game.scene.scenes[0];
                if (currentScene.matter) {
                    const debug = currentScene.matter.world.debugGraphic;
                    if (debug) {
                        debug.visible = !debug.visible;
                    } else {
                        currentScene.matter.world.createDebugGraphic();
                    }
                }
            }
        };

        window.toggleVelocityCorrection = function() {
            if (scene) scene.toggleVelocityCorrection();
        };

        window.clearLog = clearLog;

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('開始初始化反彈診斷工具...');
            
            try {
                game = new Phaser.Game(gameConfig);
                scene = game.scene.scenes[0];
                log('反彈診斷工具初始化成功');
            } catch (error) {
                log(`初始化失敗: ${error.message}`);
            }
        });
    </script>
</body>
</html>
