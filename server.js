const express = require('express');
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-here';

// MongoDB 連接
const MONGODB_URI = 'mongodb+srv://minyue1001:<EMAIL>/blockbattlearena?retryWrites=true&w=majority&appName=minyue';

// 中間件
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// 連接 MongoDB
mongoose.connect(MONGODB_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
})
.then(() => {
    console.log('✅ MongoDB 連接成功');
})
.catch((error) => {
    console.error('❌ MongoDB 連接失敗:', error);
});

// 用戶模型
const userSchema = new mongoose.Schema({
    username: {
        type: String,
        required: true,
        unique: true,
        trim: true,
        minlength: 3,
        maxlength: 20
    },
    email: {
        type: String,
        required: true,
        unique: true,
        trim: true,
        lowercase: true
    },
    password: {
        type: String,
        required: true,
        minlength: 6
    },
    level: {
        type: Number,
        default: 1
    },
    experience: {
        type: Number,
        default: 0
    },
    wins: {
        type: Number,
        default: 0
    },
    losses: {
        type: Number,
        default: 0
    },
    coins: {
        type: Number,
        default: 1000
    },
    gems: {
        type: Number,
        default: 50
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    lastLogin: {
        type: Date,
        default: Date.now
    }
});

const User = mongoose.model('User', userSchema);

// 驗證中間件
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ message: '需要登入權限' });
    }

    jwt.verify(token, JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({ message: '無效的權限' });
        }
        req.user = user;
        next();
    });
};

// 註冊 API
app.post('/api/auth/register', async (req, res) => {
    try {
        const { username, email, password } = req.body;

        // 驗證輸入
        if (!username || !email || !password) {
            return res.status(400).json({ message: '請填寫所有欄位' });
        }

        if (password.length < 6) {
            return res.status(400).json({ message: '密碼至少需要6位字符' });
        }

        // 檢查用戶是否已存在
        const existingUser = await User.findOne({
            $or: [{ username }, { email }]
        });

        if (existingUser) {
            if (existingUser.username === username) {
                return res.status(400).json({ message: '用戶名稱已被使用' });
            }
            if (existingUser.email === email) {
                return res.status(400).json({ message: '電子郵件已被使用' });
            }
        }

        // 加密密碼
        const saltRounds = 10;
        const hashedPassword = await bcrypt.hash(password, saltRounds);

        // 創建新用戶
        const newUser = new User({
            username,
            email,
            password: hashedPassword
        });

        await newUser.save();

        res.status(201).json({
            message: '註冊成功',
            user: {
                id: newUser._id,
                username: newUser.username,
                email: newUser.email
            }
        });

    } catch (error) {
        console.error('註冊錯誤:', error);
        res.status(500).json({ message: '伺服器錯誤，請稍後再試' });
    }
});

// 登入 API
app.post('/api/auth/login', async (req, res) => {
    try {
        const { username, password } = req.body;

        // 驗證輸入
        if (!username || !password) {
            return res.status(400).json({ message: '請填寫所有欄位' });
        }

        // 查找用戶
        const user = await User.findOne({ username });
        if (!user) {
            return res.status(400).json({ message: '用戶名稱或密碼錯誤' });
        }

        // 驗證密碼
        const isPasswordValid = await bcrypt.compare(password, user.password);
        if (!isPasswordValid) {
            return res.status(400).json({ message: '用戶名稱或密碼錯誤' });
        }

        // 更新最後登入時間
        user.lastLogin = new Date();
        await user.save();

        // 生成 JWT
        const token = jwt.sign(
            { userId: user._id, username: user.username },
            JWT_SECRET,
            { expiresIn: '7d' }
        );

        res.json({
            message: '登入成功',
            token,
            user: {
                id: user._id,
                username: user.username,
                email: user.email,
                level: user.level,
                experience: user.experience,
                wins: user.wins,
                losses: user.losses,
                coins: user.coins,
                gems: user.gems
            }
        });

    } catch (error) {
        console.error('登入錯誤:', error);
        res.status(500).json({ message: '伺服器錯誤，請稍後再試' });
    }
});

// 獲取用戶資料 API
app.get('/api/user/profile', authenticateToken, async (req, res) => {
    try {
        const user = await User.findById(req.user.userId).select('-password');
        if (!user) {
            return res.status(404).json({ message: '用戶不存在' });
        }

        res.json({
            user: {
                id: user._id,
                username: user.username,
                email: user.email,
                level: user.level,
                experience: user.experience,
                wins: user.wins,
                losses: user.losses,
                coins: user.coins,
                gems: user.gems,
                createdAt: user.createdAt,
                lastLogin: user.lastLogin
            }
        });

    } catch (error) {
        console.error('獲取用戶資料錯誤:', error);
        res.status(500).json({ message: '伺服器錯誤' });
    }
});

// 更新用戶資料 API
app.put('/api/user/profile', authenticateToken, async (req, res) => {
    try {
        const { level, experience, wins, losses, coins, gems } = req.body;
        
        const user = await User.findById(req.user.userId);
        if (!user) {
            return res.status(404).json({ message: '用戶不存在' });
        }

        // 更新允許的欄位
        if (level !== undefined) user.level = level;
        if (experience !== undefined) user.experience = experience;
        if (wins !== undefined) user.wins = wins;
        if (losses !== undefined) user.losses = losses;
        if (coins !== undefined) user.coins = coins;
        if (gems !== undefined) user.gems = gems;

        await user.save();

        res.json({
            message: '資料更新成功',
            user: {
                id: user._id,
                username: user.username,
                level: user.level,
                experience: user.experience,
                wins: user.wins,
                losses: user.losses,
                coins: user.coins,
                gems: user.gems
            }
        });

    } catch (error) {
        console.error('更新用戶資料錯誤:', error);
        res.status(500).json({ message: '伺服器錯誤' });
    }
});

// 驗證 token API
app.get('/api/auth/verify', authenticateToken, (req, res) => {
    res.json({ 
        message: 'Token 有效',
        user: req.user
    });
});

// 靜態文件路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'home.html'));
});

app.get('/login', (req, res) => {
    res.sendFile(path.join(__dirname, 'login.html'));
});

app.get('/register', (req, res) => {
    res.sendFile(path.join(__dirname, 'register.html'));
});

app.get('/lobby', (req, res) => {
    res.sendFile(path.join(__dirname, 'lobby.html'));
});

// 錯誤處理中間件
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ message: '伺服器內部錯誤' });
});

// 404 處理
app.use((req, res) => {
    res.status(404).json({ message: '找不到請求的資源' });
});

// 啟動服務器
app.listen(PORT, () => {
    console.log(`🚀 服務器運行在 http://localhost:${PORT}`);
    console.log(`📱 遊戲首頁: http://localhost:${PORT}`);
    console.log(`🔐 登入頁面: http://localhost:${PORT}/login`);
    console.log(`📝 註冊頁面: http://localhost:${PORT}/register`);
});

module.exports = app;
