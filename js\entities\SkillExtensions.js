/**
 * 技能擴展實現
 * Block Battle Arena - Skill Extensions
 */

// 擴展 Skill 類的新技能方法
Object.assign(Skill.prototype, {

    // 追蹤導彈
    createHomingMissile(targetX, targetY) {
        const startX = this.caster.body.position.x;
        const startY = this.caster.body.position.y;

        // 計算初始方向
        const angle = Utils.angle(startX, startY, targetX, targetY);
        const velocity = {
            x: Math.cos(angle) * this.config.SPEED / 60,
            y: Math.sin(angle) * this.config.SPEED / 60
        };

        // 創建物理實體
        this.body = this.scene.matter.add.rectangle(
            startX + Math.cos(angle) * (this.caster.size + 10),
            startY + Math.sin(angle) * (this.caster.size + 10),
            15, 15,
            {
                isSensor: true,
                collisionFilter: {
                    category: COLLISION_CATEGORIES.PROJECTILE,
                    mask: COLLISION_CATEGORIES.PLAYER | COLLISION_CATEGORIES.WALL
                }
            }
        );

        this.scene.matter.body.setVelocity(this.body, velocity);
        this.body.skillData = this;
        this.body.skillType = 'homing_missile';
        this.body.entityType = 'skill';

        // 創建視覺效果
        this.sprite = this.scene.add.triangle(
            this.body.position.x,
            this.body.position.y,
            0, -8, -6, 8, 6, 8,
            parseInt(this.config.COLOR.replace('#', '0x'))
        );

        // 追蹤參數
        this.trackingStartTime = Date.now() + 500; // 0.5秒後開始追蹤
        this.maxDistance = this.config.RANGE;
        this.traveledDistance = 0;
        this.startPosition = { x: startX, y: startY };
        this.lastTarget = null;
    },

    // 爆炸彈
    createExplosiveBomb(targetX, targetY) {
        const startX = this.caster.body.position.x;
        const startY = this.caster.body.position.y;

        // 計算投擲方向和位置
        const angle = Utils.angle(startX, startY, targetX, targetY);
        const distance = Math.min(
            Utils.distance(startX, startY, targetX, targetY),
            this.config.THROW_DISTANCE
        );

        const finalX = startX + Math.cos(angle) * distance;
        const finalY = startY + Math.sin(angle) * distance;

        // 創建炸彈實體
        this.body = this.scene.matter.add.circle(finalX, finalY, 8, {
            isSensor: true,
            collisionFilter: {
                category: COLLISION_CATEGORIES.PROJECTILE,
                mask: COLLISION_CATEGORIES.PLAYER
            }
        });

        this.body.skillData = this;
        this.body.skillType = 'explosive_bomb';
        this.body.entityType = 'skill';

        // 創建視覺效果
        this.sprite = this.scene.add.circle(
            finalX, finalY, 8,
            parseInt(this.config.COLOR.replace('#', '0x'))
        );

        // 倒數計時器
        this.explosionTime = Date.now() + this.config.DELAY;
        this.warningStarted = false;

        // 創建警告圈
        this.warningCircle = this.scene.add.circle(
            finalX, finalY, this.config.EXPLOSION_RADIUS,
            0xff0000, 0.2
        );
        this.warningCircle.setStrokeStyle(2, 0xff0000);
    },

    // 雷電鏈
    createLightningChain(targetX, targetY) {
        // 雷電鏈是即時技能，直接執行
        this.executeChain(targetX, targetY);
    },

    executeChain(targetX, targetY) {
        const startX = this.caster.body.position.x;
        const startY = this.caster.body.position.y;

        // 找到最近的目標
        let targets = this.findChainTargets(startX, startY);
        if (targets.length === 0) return;

        // 執行鏈式攻擊
        this.chainTargets = targets.slice(0, this.config.MAX_JUMPS + 1);
        this.currentChainIndex = 0;
        this.chainDamage = this.config.INITIAL_DAMAGE;

        this.executeNextChain();
    },

    findChainTargets(startX, startY) {
        const targets = [];
        const otherPlayer = this.caster.playerId === 1 ? this.scene.player2 : this.scene.player1;

        if (otherPlayer) {
            const distance = Utils.distance(
                startX, startY,
                otherPlayer.body.position.x,
                otherPlayer.body.position.y
            );

            if (distance <= this.config.JUMP_DISTANCE) {
                targets.push({
                    player: otherPlayer,
                    x: otherPlayer.body.position.x,
                    y: otherPlayer.body.position.y
                });
            }
        }

        return targets;
    },

    executeNextChain() {
        if (this.currentChainIndex >= this.chainTargets.length) {
            this.destroy();
            return;
        }

        const target = this.chainTargets[this.currentChainIndex];

        // 造成傷害
        if (target.player) {
            target.player.takeDamage(this.chainDamage, this.caster);
        }

        // 創建視覺效果
        this.createLightningEffect(target.x, target.y);

        // 準備下一次跳躍
        this.chainDamage = Math.max(1, this.chainDamage - this.config.DAMAGE_DECAY);
        this.currentChainIndex++;

        // 延遲執行下一次
        setTimeout(() => {
            this.executeNextChain();
        }, 100);
    },

    createLightningEffect(x, y) {
        const lightning = this.scene.add.line(
            0, 0,
            this.caster.body.position.x, this.caster.body.position.y,
            x, y,
            parseInt(this.config.COLOR.replace('#', '0x'))
        );
        lightning.setLineWidth(3);

        // 閃爍效果
        this.scene.tweens.add({
            targets: lightning,
            alpha: 0,
            duration: 200,
            yoyo: true,
            repeat: 2,
            onComplete: () => {
                lightning.destroy();
            }
        });
    },

    // 反射力場
    createReflectionField() {
        this.caster.effects.reflectionField = {
            duration: this.config.DURATION,
            multiplier: this.config.REFLECTION_MULTIPLIER
        };

        // 創建視覺效果
        this.sprite = this.scene.add.circle(
            this.caster.body.position.x,
            this.caster.body.position.y,
            this.caster.size + 20,
            parseInt(this.config.COLOR.replace('#', '0x')),
            0.3
        );
        this.sprite.setStrokeStyle(3, parseInt(this.config.COLOR.replace('#', '0x')));

        this.duration = this.config.DURATION;
    },

    // 幽靈模式
    createGhostMode() {
        this.caster.effects.ghostMode = {
            duration: this.config.DURATION,
            speedBonus: this.config.SPEED_BONUS
        };

        // 設置無敵和穿牆
        this.caster.setInvulnerable(this.config.DURATION);
        this.caster.body.collisionFilter.mask = COLLISION_CATEGORIES.WALL; // 只與牆壁碰撞

        // 創建視覺效果
        this.sprite = this.scene.add.circle(
            this.caster.body.position.x,
            this.caster.body.position.y,
            this.caster.size + 10,
            parseInt(this.config.COLOR.replace('#', '0x')),
            0.5
        );

        this.duration = this.config.DURATION;

        // 恢復正常碰撞
        setTimeout(() => {
            if (this.caster && this.caster.body) {
                this.caster.body.collisionFilter.mask =
                    COLLISION_CATEGORIES.PLAYER | COLLISION_CATEGORIES.WALL | COLLISION_CATEGORIES.PROJECTILE;
            }
        }, this.config.DURATION);
    },

    // 衝刺攻擊
    createDashStrike(targetX, targetY) {
        const startX = this.caster.body.position.x;
        const startY = this.caster.body.position.y;

        // 計算衝刺方向
        const angle = Utils.angle(startX, startY, targetX, targetY);
        const endX = startX + Math.cos(angle) * this.config.DISTANCE;
        const endY = startY + Math.sin(angle) * this.config.DISTANCE;

        // 確保不會衝出邊界
        const bounds = GAME_CONFIG.ARENA.BOUNDS;
        const finalX = Utils.clamp(endX, bounds.LEFT + this.caster.size/2, bounds.RIGHT - this.caster.size/2);
        const finalY = Utils.clamp(endY, bounds.TOP + this.caster.size/2, bounds.BOTTOM - this.caster.size/2);

        // 創建衝刺軌跡
        this.createDashTrail(startX, startY, finalX, finalY);

        // 瞬間移動到目標位置
        this.scene.matter.body.setPosition(this.caster.body, { x: finalX, y: finalY });

        // 檢查路徑上的敵人
        this.checkDashCollisions(startX, startY, finalX, finalY);

        this.duration = 500; // 短暫的視覺效果持續時間
    },

    createDashTrail(startX, startY, endX, endY) {
        const trail = this.scene.add.line(
            0, 0, startX, startY, endX, endY,
            parseInt(this.config.COLOR.replace('#', '0x'))
        );
        trail.setLineWidth(this.config.WIDTH);
        trail.setAlpha(0.8);

        // 淡出效果
        this.scene.tweens.add({
            targets: trail,
            alpha: 0,
            duration: 500,
            onComplete: () => {
                trail.destroy();
            }
        });
    },

    checkDashCollisions(startX, startY, endX, endY) {
        const otherPlayer = this.caster.playerId === 1 ? this.scene.player2 : this.scene.player1;
        if (!otherPlayer) return;

        // 簡化的線段碰撞檢測
        const distance = Utils.distance(
            otherPlayer.body.position.x, otherPlayer.body.position.y,
            (startX + endX) / 2, (startY + endY) / 2
        );

        if (distance <= this.config.WIDTH / 2 + otherPlayer.size / 2) {
            otherPlayer.takeDamage(this.config.DAMAGE, this.caster);
        }
    },

    // 重力井
    createGravityWell(targetX, targetY) {
        const startX = this.caster.body.position.x;
        const startY = this.caster.body.position.y;

        // 計算放置位置
        const distance = Math.min(
            Utils.distance(startX, startY, targetX, targetY),
            this.config.PLACEMENT_RANGE
        );
        const angle = Utils.angle(startX, startY, targetX, targetY);

        const finalX = startX + Math.cos(angle) * distance;
        const finalY = startY + Math.sin(angle) * distance;

        // 創建重力井
        this.position = { x: finalX, y: finalY };
        this.radius = this.config.RADIUS;
        this.pullStrength = this.config.PULL_STRENGTH;

        // 創建視覺效果
        this.sprite = this.scene.add.circle(
            finalX, finalY, this.radius,
            parseInt(this.config.COLOR.replace('#', '0x')),
            0.3
        );
        this.sprite.setStrokeStyle(3, parseInt(this.config.COLOR.replace('#', '0x')));

        this.duration = this.config.DURATION;
    }
});

// 擴展更新方法
const originalUpdate = Skill.prototype.update;
Skill.prototype.update = function(deltaTime) {
    if (!this.active) return;

    const currentTime = Date.now();
    const elapsed = currentTime - this.startTime;

    // 新技能的更新邏輯
    switch (this.skillType.toLowerCase()) {
        case 'homing_missile':
            this.updateHomingMissile(deltaTime);
            break;
        case 'explosive_bomb':
            this.updateExplosiveBomb(deltaTime);
            break;
        case 'reflection_field':
            this.updateReflectionField(deltaTime);
            break;
        case 'ghost_mode':
            this.updateGhostMode(deltaTime);
            break;
        case 'gravity_well':
            this.updateGravityWell(deltaTime);
            break;
        default:
            // 對於原始技能，調用原始更新方法
            originalUpdate.call(this, deltaTime);
            break;
    }
};

// 新技能的更新方法
Object.assign(Skill.prototype, {
    updateHomingMissile(deltaTime) {
        if (!this.body) return;

        // 更新視覺位置
        this.sprite.setPosition(this.body.position.x, this.body.position.y);

        // 檢查是否開始追蹤
        if (Date.now() > this.trackingStartTime) {
            this.updateMissileTracking();
        }

        // 檢查飛行距離
        this.traveledDistance = Utils.distance(
            this.startPosition.x, this.startPosition.y,
            this.body.position.x, this.body.position.y
        );

        if (this.traveledDistance >= this.maxDistance) {
            this.destroy();
        }
    },

    updateMissileTracking() {
        const otherPlayer = this.caster.playerId === 1 ? this.scene.player2 : this.scene.player1;
        if (!otherPlayer) return;

        const currentVel = this.body.velocity;
        const targetX = otherPlayer.body.position.x;
        const targetY = otherPlayer.body.position.y;

        // 計算朝向目標的方向
        const desiredAngle = Utils.angle(
            this.body.position.x, this.body.position.y,
            targetX, targetY
        );

        const currentAngle = Math.atan2(currentVel.y, currentVel.x);
        const speed = Utils.vectorLength(currentVel.x, currentVel.y);

        // 平滑轉向
        const angleDiff = desiredAngle - currentAngle;
        const turnRate = this.config.TURN_SPEED * Math.PI / 180 / 60; // 轉換為每幀的弧度
        const newAngle = currentAngle + Math.sign(angleDiff) * Math.min(Math.abs(angleDiff), turnRate);

        // 更新速度
        const newVelocity = {
            x: Math.cos(newAngle) * speed,
            y: Math.sin(newAngle) * speed
        };

        this.scene.matter.body.setVelocity(this.body, newVelocity);

        // 更新視覺方向
        this.sprite.setRotation(newAngle + Math.PI / 2);
    },

    updateExplosiveBomb(deltaTime) {
        const currentTime = Date.now();

        // 警告效果
        if (currentTime > this.explosionTime - 1000 && !this.warningStarted) {
            this.warningStarted = true;
            this.startWarningEffect();
        }

        // 爆炸
        if (currentTime >= this.explosionTime) {
            this.explode();
        }
    },

    startWarningEffect() {
        // 警告圈閃爍
        this.scene.tweens.add({
            targets: this.warningCircle,
            alpha: 0.8,
            duration: 200,
            yoyo: true,
            repeat: -1
        });
    },

    explode() {
        const explosionX = this.body.position.x;
        const explosionY = this.body.position.y;

        // 檢查爆炸範圍內的目標
        const otherPlayer = this.caster.playerId === 1 ? this.scene.player2 : this.scene.player1;
        if (otherPlayer) {
            const distance = Utils.distance(
                explosionX, explosionY,
                otherPlayer.body.position.x,
                otherPlayer.body.position.y
            );

            if (distance <= this.config.EXPLOSION_RADIUS) {
                // 計算傷害（距離越近傷害越高）
                const damageRatio = 1 - (distance / this.config.EXPLOSION_RADIUS);
                const damage = this.config.EDGE_DAMAGE +
                    (this.config.CENTER_DAMAGE - this.config.EDGE_DAMAGE) * damageRatio;

                otherPlayer.takeDamage(damage, this.caster);
            }
        }

        // 檢查自傷
        const selfDistance = Utils.distance(
            explosionX, explosionY,
            this.caster.body.position.x,
            this.caster.body.position.y
        );

        if (selfDistance <= this.config.EXPLOSION_RADIUS) {
            const damageRatio = 1 - (selfDistance / this.config.EXPLOSION_RADIUS);
            const damage = (this.config.EDGE_DAMAGE +
                (this.config.CENTER_DAMAGE - this.config.EDGE_DAMAGE) * damageRatio) * 0.5;

            this.caster.takeDamage(damage, this.caster);
        }

        // 創建爆炸效果
        this.createExplosionEffect(explosionX, explosionY);

        this.destroy();
    },

    createExplosionEffect(x, y) {
        // 爆炸圈
        const explosion = this.scene.add.circle(x, y, 10, 0xff6600);

        this.scene.tweens.add({
            targets: explosion,
            scaleX: this.config.EXPLOSION_RADIUS / 10,
            scaleY: this.config.EXPLOSION_RADIUS / 10,
            alpha: 0,
            duration: 300,
            onComplete: () => {
                explosion.destroy();
            }
        });

        // 粒子效果
        const particles = Utils.createParticleData(x, y, 20, '#ff6600');
        if (this.caster && this.caster.particles) {
            this.caster.particles.push(...particles);
        }
    },

    updateGravityWell(deltaTime) {
        if (!this.position) return;

        // 影響範圍內的所有物體
        const players = [this.scene.player1, this.scene.player2].filter(p => p);

        players.forEach(player => {
            const distance = Utils.distance(
                this.position.x, this.position.y,
                player.body.position.x, player.body.position.y
            );

            if (distance <= this.radius && distance > 0) {
                // 計算吸引力
                const pullForce = this.pullStrength * (1 - distance / this.radius);
                const angle = Utils.angle(
                    player.body.position.x, player.body.position.y,
                    this.position.x, this.position.y
                );

                // 應用力
                const force = {
                    x: Math.cos(angle) * pullForce / 60,
                    y: Math.sin(angle) * pullForce / 60
                };

                this.scene.matter.body.applyForce(player.body, player.body.position, force);
            }
        });

        // 更新視覺效果
        const pulse = 1 + Math.sin(Date.now() * 0.01) * 0.2;
        this.sprite.setScale(pulse);
    },

    updateReflectionField(deltaTime) {
        if (!this.caster.effects.reflectionField) {
            this.destroy();
            return;
        }

        // 更新位置跟隨玩家
        this.sprite.setPosition(this.caster.body.position.x, this.caster.body.position.y);

        // 脈衝效果
        const pulse = 1 + Math.sin(Date.now() * 0.008) * 0.15;
        this.sprite.setScale(pulse);
    },

    updateGhostMode(deltaTime) {
        if (!this.caster.effects.ghostMode) {
            this.destroy();
            return;
        }

        // 更新位置跟隨玩家
        this.sprite.setPosition(this.caster.body.position.x, this.caster.body.position.y);

        // 閃爍效果
        const alpha = 0.3 + 0.2 * Math.sin(Date.now() * 0.02);
        this.sprite.setAlpha(alpha);
    }
});
