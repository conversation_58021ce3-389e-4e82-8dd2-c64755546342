/**
 * 主選單場景
 * Block Battle Arena - Menu Scene
 */

class MenuScene extends Phaser.Scene {
    constructor() {
        super({ key: 'MenuScene' });
    }

    preload() {
        // 載入資源 (目前使用程序生成的圖形)
        Utils.log('MenuScene: Loading assets...');
    }

    create() {
        console.log('MenuScene: Creating menu...');

        // 隱藏載入畫面
        this.hideLoadingScreen();

        // 隱藏遊戲 UI 覆蓋層（在選單中不需要）
        const uiOverlay = document.querySelector('#ui-overlay');
        if (uiOverlay) {
            uiOverlay.style.display = 'none';
        }

        // 背景
        this.add.rectangle(
            GAME_CONFIG.SCREEN.WIDTH / 2,
            GAME_CONFIG.SCREEN.HEIGHT / 2,
            GAME_CONFIG.SCREEN.WIDTH,
            GAME_CONFIG.SCREEN.HEIGHT,
            0x1a1a2e
        );

        // 標題
        const title = this.add.text(
            GAME_CONFIG.SCREEN.WIDTH / 2,
            150,
            'Block Battle Arena',
            {
                fontSize: '48px',
                fill: '#ffffff',
                fontFamily: 'Arial',
                stroke: '#00d4ff',
                strokeThickness: 3
            }
        ).setOrigin(0.5);

        // 副標題
        this.add.text(
            GAME_CONFIG.SCREEN.WIDTH / 2,
            200,
            '方塊競技場',
            {
                fontSize: '24px',
                fill: '#cccccc',
                fontFamily: 'Arial'
            }
        ).setOrigin(0.5);

        // 快速開始按鈕
        const quickStartButton = this.add.rectangle(
            GAME_CONFIG.SCREEN.WIDTH / 2,
            280,
            200, 50,
            0x00d4ff
        ).setInteractive();

        this.add.text(
            GAME_CONFIG.SCREEN.WIDTH / 2,
            280,
            '快速開始',
            {
                fontSize: '18px',
                fill: '#000000',
                fontFamily: 'Arial'
            }
        ).setOrigin(0.5);

        // 技能選擇按鈕
        const skillSelectButton = this.add.rectangle(
            GAME_CONFIG.SCREEN.WIDTH / 2,
            340,
            200, 50,
            0xff6b35
        ).setInteractive();

        this.add.text(
            GAME_CONFIG.SCREEN.WIDTH / 2,
            340,
            '技能選擇',
            {
                fontSize: '18px',
                fill: '#000000',
                fontFamily: 'Arial'
            }
        ).setOrigin(0.5);

        // 按鈕事件
        quickStartButton.on('pointerdown', () => {
            console.log('Starting quick game...');
            try {
                this.scene.start('GameScene', {
                    playerSkills: ['SHOCKWAVE', 'SHIELD', 'TELEPORT', 'BOOST'],
                    loadoutName: '新手友善'
                });
            } catch (error) {
                console.error('Failed to start game:', error);
                // 如果 GameScene 有問題，跳轉到手動反彈版本
                window.location.href = 'manual-bounce-game.html';
            }
        });

        skillSelectButton.on('pointerdown', () => {
            console.log('Opening skill selection...');
            try {
                this.scene.start('SkillSelectScene');
            } catch (error) {
                console.error('Failed to open skill selection:', error);
                // 如果技能選擇有問題，直接開始遊戲
                this.scene.start('GameScene', {
                    playerSkills: ['SHOCKWAVE', 'SHIELD', 'TELEPORT', 'BOOST'],
                    loadoutName: '預設'
                });
            }
        });

        // 懸停效果
        quickStartButton.on('pointerover', () => {
            quickStartButton.setFillStyle(0x0099cc);
        });

        quickStartButton.on('pointerout', () => {
            quickStartButton.setFillStyle(0x00d4ff);
        });

        skillSelectButton.on('pointerover', () => {
            skillSelectButton.setFillStyle(0xcc5529);
        });

        skillSelectButton.on('pointerout', () => {
            skillSelectButton.setFillStyle(0xff6b35);
        });

        // 控制說明
        this.add.text(
            GAME_CONFIG.SCREEN.WIDTH / 2,
            450,
            '控制說明:\n空白鍵: 切換模式\nQ/W/E/R: 技能\n滑鼠: 瞄準方向',
            {
                fontSize: '16px',
                fill: '#cccccc',
                fontFamily: 'Arial',
                align: 'center'
            }
        ).setOrigin(0.5);

        // 版本信息
        this.add.text(
            20, GAME_CONFIG.SCREEN.HEIGHT - 30,
            'Alpha v0.1.0',
            {
                fontSize: '12px',
                fill: '#666666',
                fontFamily: 'Arial'
            }
        );
    }

    hideLoadingScreen() {
        const loadingScreen = document.querySelector('#loading-screen');
        if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
                loadingScreen.style.display = 'none';
            }, 500);
        }
    }
}
