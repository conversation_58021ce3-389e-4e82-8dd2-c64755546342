{"name": "block-battle-arena", "version": "1.0.0", "description": "Block Battle Arena - 2D Physics Battle Game", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "client": "http-server -p 8080 -c-1", "client-dev": "http-server -p 8080 -c-1 -o", "build": "echo 'Build process not implemented yet'", "test": "echo 'Tests not implemented yet'"}, "keywords": ["game", "phaser", "physics", "battle", "multiplayer"], "author": "Block Battle Arena Team", "license": "MIT", "dependencies": {"phaser": "^3.70.0", "matter-js": "^0.19.0", "express": "^4.18.2", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5"}, "devDependencies": {"http-server": "^14.1.1", "nodemon": "^3.0.1"}}