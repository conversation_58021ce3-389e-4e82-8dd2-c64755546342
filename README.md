# Block Battle Arena - 方塊競技場

基於 Phaser.js 和 Matter.js 的 2D 物理對戰遊戲

## 🎮 遊戲概述

Block Battle Arena 是一個創新的 2D 物理對戰遊戲，兩個方塊在封閉場地內進行永續運動的戰鬥。玩家通過切換戰鬥模式和使用技能來擊敗對手。

### 核心特色
- **永續物理運動** - 方塊永不停止移動
- **智能戰鬥模式** - 進攻/防守/中性模式切換
- **技能戰鬥系統** - 4種基礎技能組合
- **純技巧對決** - 無數值成長，完全依靠技巧

## 🚀 快速開始

### 環境需求
- Node.js 14.0 或更高版本
- 現代瀏覽器 (Chrome, Firefox, Safari, Edge)

### 安裝步驟

1. **克隆項目**
```bash
git clone <repository-url>
cd block-battle-arena
```

2. **安裝依賴**
```bash
npm install
```

3. **啟動開發服務器**
```bash
npm run dev
```

4. **打開瀏覽器**
訪問 http://localhost:8080

## 🎯 遊戲控制

### 玩家1控制
- **空白鍵**: 切換戰鬥模式 (進攻/防守/中性)
- **Q**: 衝擊波 - 發射能量波攻擊
- **W**: 能量護盾 - 吸收傷害
- **E**: 瞬移 - 瞬間移動到滑鼠位置
- **R**: 瞬間加速 - 提升移動速度
- **滑鼠**: 瞄準技能方向

### 玩家2控制 (如果是雙人模式)
- **Enter**: 切換戰鬥模式
- **U/I/O/P**: 對應技能

## 🎨 遊戲機制

### 戰鬥模式
1. **進攻模式** (紅色光環)
   - 主動追蹤對手
   - 技能冷卻 -20%
   - 碰撞傷害 +20%

2. **防守模式** (藍色光環)
   - 主動遠離對手
   - 受到傷害 -20%
   - 護盾效果 +25%

3. **中性模式** (白色光環)
   - 隨機移動
   - 標準數值

### 技能系統

#### 🗡️ 攻擊技能
- **衝擊波** - 發射直線能量波 (25傷害, 3秒冷卻)
- **追蹤導彈** - 發射會追蹤敵人的導彈 (20傷害, 4秒冷卻)
- **爆炸彈** - 投擲延時爆炸彈 (15-35傷害, 6秒冷卻)
- **雷電鏈** - 雷電鏈式攻擊，可跳躍3次 (18傷害遞減, 5秒冷卻)

#### 🛡️ 防禦技能
- **能量護盾** - 吸收50點傷害 (5秒持續, 8秒冷卻)
- **反射力場** - 反射敵方攻擊並增強威力 (3秒持續, 12秒冷卻)
- **幽靈模式** - 短暫無敵並可穿牆 (2秒持續, 10秒冷卻)

#### 🏃 移動技能
- **瞬移** - 瞬間移動到目標位置 (200像素範圍, 8秒冷卻)
- **瞬間加速** - 移動速度翻倍 (2秒持續, 6秒冷卻)
- **衝刺攻擊** - 衝刺穿越敵人造成傷害 (20傷害, 7秒冷卻)

#### 🎯 控制技能
- **重力井** - 創建吸引物體的重力場 (4秒持續, 10秒冷卻)
- **能量牆** - 創建阻擋移動的能量牆 (6秒持續, 8秒冷卻)
- **冰凍射線** - 減緩敵人移動速度 (3秒效果, 9秒冷卻)

#### 🔗 技能組合
- **新手友善**: 衝擊波 + 護盾 + 加速 + 重力井
- **進攻型**: 追蹤彈 + 衝刺攻擊 + 瞬移 + 爆炸彈
- **防守型**: 雷電鏈 + 反射場 + 幽靈化 + 能量牆
- **控制型**: 重力井 + 冰凍射線 + 能量牆 + 雷電鏈

## 🏗️ 項目結構

```
block-battle-arena/
├── index.html              # 主頁面
├── package.json            # 項目配置
├── js/
│   ├── config.js          # 遊戲配置
│   ├── utils.js           # 工具函數
│   ├── main.js            # 遊戲啟動
│   ├── entities/
│   │   ├── Player.js      # 玩家實體
│   │   └── Skill.js       # 技能實體
│   ├── managers/
│   │   ├── SkillManager.js # 技能管理
│   │   └── UIManager.js    # UI管理
│   └── scenes/
│       ├── MenuScene.js    # 主選單場景
│       ├── GameScene.js    # 遊戲場景
│       └── GameOverScene.js # 結束場景
└── Block_Battle_Arena_Documents/ # 設計文檔
```

## 🔧 開發指南

### 配置修改
主要配置在 `js/config.js` 中：
- 遊戲畫面尺寸
- 玩家屬性
- 技能數值
- 物理參數

### 添加新技能
1. 在 `GAME_CONFIG.SKILLS` 中添加配置
2. 在 `Skill.js` 中實現技能邏輯
3. 在 `SkillManager.js` 中添加技能處理

### 調試功能
- 按 F12 打開開發者工具查看日誌
- 修改 `GAME_CONFIG.DEBUG` 開啟調試功能
- 使用 `window.GameControl` 控制遊戲狀態

## 🎯 當前實現狀態

### ✅ 已完成功能
- [x] 基礎物理引擎集成
- [x] 雙方塊永續運動
- [x] 三種戰鬥模式切換
- [x] **13種技能系統** - 攻擊/防禦/移動/控制四大類
- [x] **技能選擇系統** - 4種預設組合 + 自定義配置
- [x] **技能協同效應** - 技能組合產生特殊效果
- [x] 碰撞檢測和傷害計算
- [x] UI界面和血量顯示
- [x] 技能冷卻系統
- [x] **程序化音效系統** - Web Audio API 生成音效
- [x] 簡單AI對手（隨機技能組合）
- [x] 遊戲勝負判定

### 🚧 開發中功能
- [ ] 音效系統
- [ ] 粒子效果優化
- [ ] 更多技能
- [ ] 多種場地
- [ ] 網路多人對戰

### 📋 計劃功能
- [ ] 進度系統
- [ ] 成就系統
- [ ] 排位匹配
- [ ] 觀戰模式
- [ ] 回放系統

## 🐛 已知問題

1. 粒子效果性能需要優化
2. AI決策邏輯較為簡單
3. 缺少音效反饋
4. 移動設備適配待完善

## 🤝 貢獻指南

1. Fork 項目
2. 創建功能分支
3. 提交更改
4. 發起 Pull Request

## 📄 授權

MIT License - 詳見 LICENSE 文件

## 📞 聯絡方式

- 項目文檔: `Block_Battle_Arena_Documents/`
- 問題回報: GitHub Issues
- 功能建議: GitHub Discussions

## 🔐 用戶系統 (新增)

### 功能特色
- **完整的用戶系統**：註冊、登入、用戶資料管理
- **MongoDB 數據庫**：安全的用戶數據存儲
- **JWT 認證**：安全的 Token 驗證機制
- **密碼加密**：使用 bcryptjs 加密用戶密碼

### 啟動完整系統
```bash
# 安裝後端依賴
npm install

# 啟動完整服務器 (包含用戶系統)
npm start

# 或使用啟動腳本
start.bat
```

### 訪問地址
- 🏠 **遊戲首頁**: http://localhost:3000
- 🔐 **登入頁面**: http://localhost:3000/login
- 📝 **註冊頁面**: http://localhost:3000/register
- 🎮 **遊戲大廳**: http://localhost:3000/lobby

### API 端點
- `POST /api/auth/register` - 用戶註冊
- `POST /api/auth/login` - 用戶登入
- `GET /api/auth/verify` - 驗證 Token
- `GET /api/user/profile` - 獲取用戶資料
- `PUT /api/user/profile` - 更新用戶資料

### 數據庫結構
```javascript
User {
  username: String,      // 用戶名稱
  email: String,         // 電子郵件
  password: String,      // 加密密碼
  level: Number,         // 等級
  experience: Number,    // 經驗值
  wins: Number,          // 勝利次數
  losses: Number,        // 失敗次數
  coins: Number,         // 金幣
  gems: Number,          // 寶石
  createdAt: Date,       // 創建時間
  lastLogin: Date        // 最後登入時間
}
```

---

**享受遊戲！記住：這是純技巧對決，沒有運氣成分！** 🎮
