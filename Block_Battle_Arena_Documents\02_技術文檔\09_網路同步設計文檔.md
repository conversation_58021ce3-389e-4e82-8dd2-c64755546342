# 網路同步設計文檔
## Block Battle Arena - Network Synchronization Design Document

---

## 📋 文檔資訊
- **版本號**: v1.0
- **創建日期**: 2025年6月3日
- **最後更新**: 2025年6月3日
- **關聯文檔**: 技術架構文檔、物理引擎設計文檔

---

## 🎯 網路同步概述

### 設計目標
- **低延遲**: 玩家操作延遲控制在50ms以內
- **一致性**: 確保所有客戶端看到相同的遊戲狀態
- **流暢性**: 60FPS穩定運行，無明顯卡頓
- **容錯性**: 處理網路波動、丟包、斷線重連

### 核心挑戰
- **物理同步**: 永不停歇的方塊運動同步
- **實時性**: 快節奏戰鬥的即時反饋
- **精確性**: 碰撞檢測和傷害判定的準確性
- **公平性**: 防止網路優勢和作弊行為

---

## 🏗️ 同步架構設計

### 整體架構模式
```
客戶端A ←→ 遊戲服務器 ←→ 客戶端B
    ↓         ↓           ↓
 本地預測   權威狀態    本地預測
 插值渲染   衝突解決    插值渲染
 輸入緩衝   延遲補償    輸入緩衝
```

### 網路拓撲結構
- **權威服務器**: 所有遊戲邏輯在服務器端執行
- **客戶端預測**: 本地立即響應玩家輸入
- **狀態同步**: 定期同步完整遊戲狀態
- **事件同步**: 即時同步關鍵遊戲事件

---

## 📡 同步協議設計

### Socket.io 事件架構
```typescript
// 客戶端到服務器事件
interface ClientToServerEvents {
  // 玩家輸入
  'player:input': (input: PlayerInput) => void;
  'player:skill': (skillData: SkillUsage) => void;
  'player:mode_switch': (mode: 'offensive' | 'defensive') => void;
  
  // 連接管理
  'connection:ping': (timestamp: number) => void;
  'connection:ready': () => void;
  'connection:request_sync': () => void;
  
  // 遊戲控制
  'game:pause_request': () => void;
  'game:surrender': () => void;
}

// 服務器到客戶端事件
interface ServerToClientEvents {
  // 狀態同步
  'game:state_update': (state: GameStateSnapshot) => void;
  'game:delta_update': (delta: StateDelta) => void;
  'game:full_sync': (fullState: CompleteGameState) => void;
  
  // 事件通知
  'game:player_action': (action: PlayerAction) => void;
  'game:collision': (collision: CollisionEvent) => void;
  'game:skill_effect': (effect: SkillEffect) => void;
  
  // 連接管理
  'connection:pong': (timestamp: number) => void;
  'connection:latency': (latency: number) => void;
  'connection:sync_required': () => void;
  
  // 遊戲事件
  'game:player_joined': (player: PlayerData) => void;
  'game:player_left': (playerId: string) => void;
  'game:match_end': (result: MatchResult) => void;
}
```

### 數據包結構設計
```typescript
// 玩家輸入數據包
interface PlayerInput {
  playerId: string;
  sequence: number;        // 序列號，用於確認和重排
  timestamp: number;       // 客戶端時間戳
  input: {
    movement: Vector2;     // 移動方向 (-1到1)
    actions: string[];     // 按鍵動作列表
  };
  clientPrediction: {
    position: Vector2;     // 客戶端預測位置
    velocity: Vector2;     // 客戶端預測速度
  };
}

// 遊戲狀態快照
interface GameStateSnapshot {
  timestamp: number;       // 服務器時間戳
  sequence: number;        // 狀態序列號
  players: {
    [playerId: string]: {
      position: Vector2;
      velocity: Vector2;
      health: number;
      mode: 'offensive' | 'defensive';
      cooldowns: { [skillId: string]: number };
    };
  };
  projectiles: ProjectileState[];
  effects: EffectState[];
  gameTime: number;        // 遊戲時間
}

// 增量更新數據
interface StateDelta {
  timestamp: number;
  sequence: number;
  changes: {
    players?: Partial<{ [playerId: string]: Partial<PlayerState> }>;
    projectiles?: {
      added?: ProjectileState[];
      updated?: Partial<ProjectileState>[];
      removed?: string[];
    };
    effects?: {
      added?: EffectState[];
      removed?: string[];
    };
  };
}
```

---

## ⚡ 客戶端預測系統

### 輸入預測機制
```typescript
class ClientPrediction {
  private inputBuffer: PlayerInput[] = [];
  private stateHistory: GameStateSnapshot[] = [];
  private maxHistorySize: number = 60; // 1秒的歷史(60fps)
  
  // 處理本地輸入
  handleLocalInput(input: PlayerInput): void {
    // 1. 立即應用到本地遊戲狀態
    this.applyInputLocally(input);
    
    // 2. 存儲輸入到緩衝區
    this.inputBuffer.push(input);
    
    // 3. 發送到服務器
    this.sendInputToServer(input);
    
    // 4. 清理舊的輸入
    this.cleanupOldInputs();
  }
  
  private applyInputLocally(input: PlayerInput): void {
    const localPlayer = GameState.getLocalPlayer();
    
    // 應用移動
    if (input.input.movement.x !== 0 || input.input.movement.y !== 0) {
      localPlayer.physics.applyMovement(
        input.input.movement, 
        16.67 // 60fps時間步長
      );
    }
    
    // 應用技能
    input.input.actions.forEach(action => {
      switch (action) {
        case 'skill_1':
          if (localPlayer.canUseSkill(0)) {
            localPlayer.useSkill(0);
          }
          break;
        case 'mode_switch':
          localPlayer.switchMode();
          break;
      }
    });
  }
  
  // 服務器校正處理
  handleServerCorrection(serverState: GameStateSnapshot): void {
    // 1. 找到對應的本地狀態
    const localState = this.findLocalStateByTimestamp(serverState.timestamp);
    
    if (!localState) {
      // 沒有對應狀態，直接應用服務器狀態
      this.applyServerState(serverState);
      return;
    }
    
    // 2. 計算差異
    const discrepancy = this.calculateDiscrepancy(localState, serverState);
    
    // 3. 判斷是否需要校正
    if (discrepancy > CORRECTION_THRESHOLD) {
      this.performCorrection(serverState);
    }
  }
  
  private performCorrection(serverState: GameStateSnapshot): void {
    // 1. 回滾到服務器狀態時間點
    this.rollbackToTimestamp(serverState.timestamp);
    
    // 2. 應用服務器狀態
    this.applyServerState(serverState);
    
    // 3. 重新應用之後的輸入
    const futureInputs = this.getInputsAfterTimestamp(serverState.timestamp);
    futureInputs.forEach(input => {
      this.applyInputLocally(input);
    });
  }
}
```

### 插值和外推系統
```typescript
class InterpolationSystem {
  private stateBuffer: GameStateSnapshot[] = [];
  private interpolationDelay: number = 100; // 100ms延遲
  
  // 接收服務器狀態
  receiveServerState(state: GameStateSnapshot): void {
    // 按時間戳排序插入
    this.insertStateInOrder(state);
    
    // 清理過舊的狀態
    this.cleanupOldStates();
  }
  
  // 獲取插值後的狀態
  getInterpolatedState(): GameStateSnapshot {
    const now = Date.now();
    const renderTime = now - this.interpolationDelay;
    
    // 找到渲染時間點前後的兩個狀態
    const [prevState, nextState] = this.findSurroundingStates(renderTime);
    
    if (!prevState || !nextState) {
      // 使用外推法
      return this.extrapolateState(prevState || nextState, renderTime);
    }
    
    // 線性插值
    const alpha = (renderTime - prevState.timestamp) / 
                  (nextState.timestamp - prevState.timestamp);
    
    return this.interpolateStates(prevState, nextState, alpha);
  }
  
  private interpolateStates(
    prev: GameStateSnapshot, 
    next: GameStateSnapshot, 
    alpha: number
  ): GameStateSnapshot {
    const interpolated: GameStateSnapshot = {
      timestamp: prev.timestamp + (next.timestamp - prev.timestamp) * alpha,
      sequence: prev.sequence,
      players: {},
      projectiles: [],
      effects: [],
      gameTime: prev.gameTime + (next.gameTime - prev.gameTime) * alpha
    };
    
    // 插值玩家狀態
    Object.keys(prev.players).forEach(playerId => {
      const prevPlayer = prev.players[playerId];
      const nextPlayer = next.players[playerId];
      
      if (prevPlayer && nextPlayer) {
        interpolated.players[playerId] = {
          position: this.lerpVector2(prevPlayer.position, nextPlayer.position, alpha),
          velocity: this.lerpVector2(prevPlayer.velocity, nextPlayer.velocity, alpha),
          health: Math.round(prevPlayer.health + (nextPlayer.health - prevPlayer.health) * alpha),
          mode: alpha < 0.5 ? prevPlayer.mode : nextPlayer.mode,
          cooldowns: { ...prevPlayer.cooldowns } // 冷卻時間不插值
        };
      }
    });
    
    return interpolated;
  }
}
```

---

## 🔄 服務器權威系統

### 遊戲狀態管理
```typescript
class AuthoritativeGameServer {
  private gameState: GameState;
  private inputBuffer: Map<string, PlayerInput[]> = new Map();
  private stateHistory: GameStateSnapshot[] = [];
  private tickRate: number = 60; // 60 TPS
  private updateRate: number = 20; // 20次/秒發送更新
  
  constructor() {
    this.gameState = new GameState();
    this.startGameLoop();
  }
  
  private startGameLoop(): void {
    const tickInterval = 1000 / this.tickRate;
    
    setInterval(() => {
      this.processTick();
    }, tickInterval);
    
    // 狀態同步循環
    const updateInterval = 1000 / this.updateRate;
    setInterval(() => {
      this.sendStateUpdates();
    }, updateInterval);
  }
  
  private processTick(): void {
    const currentTime = Date.now();
    
    // 1. 處理所有玩家輸入
    this.processPlayerInputs(currentTime);
    
    // 2. 更新物理世界
    this.gameState.updatePhysics(1000 / this.tickRate);
    
    // 3. 處理碰撞和遊戲邏輯
    this.gameState.processGameLogic();
    
    // 4. 保存狀態歷史
    this.saveStateSnapshot(currentTime);
    
    // 5. 清理過期數據
    this.cleanupOldData();
  }
  
  // 接收客戶端輸入
  receivePlayerInput(playerId: string, input: PlayerInput): void {
    // 驗證輸入合法性
    if (!this.validateInput(playerId, input)) {
      console.warn(`Invalid input from player ${playerId}:`, input);
      return;
    }
    
    // 檢查輸入時序
    if (!this.validateInputTiming(playerId, input)) {
      console.warn(`Input timing violation from player ${playerId}`);
      return;
    }
    
    // 添加到輸入緩衝區
    if (!this.inputBuffer.has(playerId)) {
      this.inputBuffer.set(playerId, []);
    }
    
    this.inputBuffer.get(playerId).push(input);
    
    // 發送確認
    this.sendInputConfirmation(playerId, input.sequence);
  }
}
```

---

## 🛡️ 容錯和重連機制

### 斷線檢測和重連
```typescript
class ConnectionManager {
  private connections: Map<string, PlayerConnection> = new Map();
  private heartbeatInterval: number = 1000; // 1秒心跳
  private connectionTimeout: number = 5000; // 5秒超時
  
  // 玩家連接管理
  handlePlayerConnection(playerId: string, socket: Socket): void {
    const connection: PlayerConnection = {
      playerId,
      socket,
      lastHeartbeat: Date.now(),
      isConnected: true,
      reconnectAttempts: 0,
      savedState: null
    };
    
    this.connections.set(playerId, connection);
    this.setupHeartbeat(connection);
    this.setupReconnectHandlers(connection);
  }
  
  private handleConnectionLost(connection: PlayerConnection): void {
    console.log(`Player ${connection.playerId} connection lost`);
    
    connection.isConnected = false;
    
    // 保存玩家狀態
    connection.savedState = this.gameState.getPlayerState(connection.playerId);
    
    // 暫停玩家控制
    this.gameState.setPlayerInactive(connection.playerId);
    
    // 通知其他玩家
    this.broadcastPlayerDisconnected(connection.playerId);
    
    // 設置重連等待期
    this.startReconnectWindow(connection);
  }
  
  private handleReconnectRequest(
    connection: PlayerConnection, 
    data: ReconnectData
  ): void {
    console.log(`Player ${connection.playerId} attempting to reconnect`);
    
    // 驗證重連請求
    if (!this.validateReconnectRequest(connection, data)) {
      connection.socket.emit('reconnect_failed', 'Invalid reconnect data');
      return;
    }
    
    // 恢復連接
    connection.isConnected = true;
    connection.lastHeartbeat = Date.now();
    connection.reconnectAttempts++;
    
    // 恢復玩家狀態
    if (connection.savedState) {
      this.gameState.restorePlayerState(connection.playerId, connection.savedState);
      connection.savedState = null;
    }
    
    // 發送完整狀態同步
    const fullState = this.gameState.createSnapshot();
    connection.socket.emit('reconnect_success', {
      gameState: fullState,
      playerId: connection.playerId,
      serverTime: Date.now()
    });
    
    // 重新激活玩家
    this.gameState.setPlayerActive(connection.playerId);
    
    console.log(`Player ${connection.playerId} reconnected successfully`);
  }
}
```

---

## 🔐 安全和防作弊

### 輸入驗證和限制
```typescript
class AntiCheatSystem {
  private playerInputHistory: Map<string, InputHistoryEntry[]> = new Map();
  private suspiciousActivity: Map<string, SuspiciousActivity[]> = new Map();
  private rateLimiter: RateLimiter;
  
  constructor() {
    this.rateLimiter = new RateLimiter({
      maxInputsPerSecond: 30,    // 最多30個輸入/秒
      maxActionsPerSecond: 10,   // 最多10個動作/秒
      burstLimit: 5              // 突發限制
    });
  }
  
  // 驗證玩家輸入
  validatePlayerInput(playerId: string, input: PlayerInput): ValidationResult {
    // 1. 速率限制檢查
    if (!this.rateLimiter.checkRate(playerId, input)) {
      this.recordSuspiciousActivity(playerId, {
        type: 'rate_limit_violation',
        timestamp: Date.now(),
        data: { inputsPerSecond: this.rateLimiter.getCurrentRate(playerId) }
      });
      return { valid: false, reason: 'Rate limit exceeded' };
    }
    
    // 2. 輸入合理性檢查
    const reasonabilityCheck = this.checkInputReasonability(playerId, input);
    if (!reasonabilityCheck.valid) {
      return reasonabilityCheck;
    }
    
    // 3. 反應時間檢查
    const reactionCheck = this.checkReactionTime(playerId, input);
    if (!reactionCheck.valid) {
      return reactionCheck;
    }
    
    // 記錄合法輸入
    this.recordInputHistory(playerId, input);
    
    return { valid: true };
  }
  
  private checkInputReasonability(playerId: string, input: PlayerInput): ValidationResult {
    // 檢查移動向量合理性
    const movement = input.input.movement;
    const magnitude = Math.sqrt(movement.x * movement.x + movement.y * movement.y);
    
    if (magnitude > 1.01) { // 允許小量浮點誤差
      this.recordSuspiciousActivity(playerId, {
        type: 'impossible_input',
        timestamp: Date.now(),
        data: { movement, magnitude }
      });
      return { valid: false, reason: 'Invalid movement magnitude' };
    }
    
    return { valid: true };
  }
}
```

---

## 📊 性能監控和調優

### 網路性能監控
```typescript
class NetworkPerformanceMonitor {
  private metrics: NetworkMetrics = {
    totalPacketsSent: 0,
    totalPacketsReceived: 0,
    totalBytesSent: 0,
    totalBytesReceived: 0,
    averageLatency: 0,
    packetLossRate: 0,
    bandwidthUtilization: 0
  };
  
  // 記錄網路指標
  recordNetworkMetrics(playerId: string, metrics: PlayerNetworkMetrics): void {
    // 更新全局指標
    this.metrics.totalPacketsSent += metrics.packetsSent;
    this.metrics.totalPacketsReceived += metrics.packetsReceived;
    
    // 計算平均延遲
    this.updateAverageLatency(metrics.latency);
    
    // 更新丟包率
    this.updatePacketLossRate(metrics.packetLoss);
  }
  
  // 生成性能報告
  generatePerformanceReport(): NetworkPerformanceReport {
    return {
      timestamp: new Date(),
      metrics: { ...this.metrics },
      playerMetrics: this.getPlayerMetrics(),
      serverLoad: this.getServerLoad(),
      recommendations: this.generateRecommendations()
    };
  }
  
  // 自動調優
  performAutoTuning(): void {
    const report = this.generatePerformanceReport();
    
    // 基於性能自動調整參數
    if (report.metrics.averageLatency > 150) {
      // 高延遲環境，降低更新頻率
      this.gameServer.setUpdateRate(15);
      this.interpolationSystem.setGlobalDelay(200);
    } else if (report.metrics.averageLatency < 50) {
      // 低延遲環境，提高更新頻率
      this.gameServer.setUpdateRate(25);
      this.interpolationSystem.setGlobalDelay(50);
    }
  }
}
```

---

## 📋 接口定義

### 網路同步接口
```typescript
// 玩家輸入接口
interface PlayerInput {
  playerId: string;
  sequence: number;
  timestamp: number;
  input: {
    movement: Vector2;
    actions: string[];
  };
  clientPrediction: {
    position: Vector2;
    velocity: Vector2;
  };
}

// 遊戲狀態快照接口
interface GameStateSnapshot {
  timestamp: number;
  sequence: number;
  players: { [playerId: string]: PlayerState };
  projectiles: ProjectileState[];
  effects: EffectState[];
  gameTime: number;
}

// 網路品質接口
interface NetworkQuality {
  score: number;
  latency: number;
  packetLoss: number;
  jitter: number;
  level: 'excellent' | 'good' | 'fair' | 'poor';
}

// 驗證結果接口
interface ValidationResult {
  valid: boolean;
  reason?: string;
  data?: any;
}

// 網路指標接口
interface NetworkMetrics {
  totalPacketsSent: number;
  totalPacketsReceived: number;
  totalBytesSent: number;
  totalBytesReceived: number;
  averageLatency: number;
  packetLossRate: number;
  bandwidthUtilization: number;
}
```

---

## 🎯 最佳實踐

### 延遲優化建議
1. **客戶端預測**: 立即響應本地輸入，提供即時反饋
2. **插值渲染**: 平滑顯示其他玩家的動作
3. **延遲補償**: 服務器回滾處理高延遲玩家的輸入
4. **自適應頻率**: 根據網路條件動態調整更新頻率

### 一致性保證
1. **服務器權威**: 所有關鍵判定在服務器執行
2. **確定性物理**: 使用固定時間步長確保可重現性
3. **狀態校正**: 定期校正客戶端狀態偏差
4. **衝突解決**: 清晰的衝突解決策略

### 性能優化
1. **數據壓縮**: 多層次的數據壓縮策略
2. **增量更新**: 只發送變化的狀態數據
3. **空間分割**: 只同步可見範圍內的對象
4. **頻寬管理**: 根據玩家網路條件調整數據量

### 安全措施
1. **輸入驗證**: 嚴格驗證所有客戶端輸入
2. **反作弊檢測**: 多維度檢測異常行為
3. **狀態驗證**: 定期驗證客戶端狀態合理性
4. **率限制**: 防止惡意的高頻輸入

---

## 📞 聯絡資訊

**網路同步負責人**: [待指定]
**性能優化專家**: [待指定]
**安全工程師**: [待指定]
**測試工程師**: [待指定]

**技術討論**: #network-sync (Discord/Slack)
**問題回報**: GitHub Issues
**文檔更新**: 每次網路協議變更時同步更新

---

## 🎉 結語

本網路同步設計文檔為Block Battle Arena提供了完整的多人遊戲網路解決方案。通過精心設計的同步機制、延遲補償技術和反作弊系統，我們能夠實現流暢、公平且安全的線上對戰體驗。

**核心特色**:
- **低延遲體驗**: 客戶端預測 + 服務器校正的混合架構
- **智能適應**: 根據網路條件自動調整同步策略
- **可靠性保證**: 斷線重連和容錯機制
- **安全防護**: 多層次的反作弊和輸入驗證
- **性能優化**: 數據壓縮和頻寬管理

隨著遊戲開發的進行，此文檔將持續更新以反映最新的網路同步實現和優化策略。所有團隊成員都應該熟悉網路同步的工作原理，確保遊戲體驗的一致性和品質。

**記住**: 優秀的網路同步是線上遊戲成功的關鍵！

---

*本文檔最後更新: 2025年6月3日*
*版本: v1.0*
*下次檢視時間: 每次重大網路功能更新時*