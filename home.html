<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Block Battle Arena - 終極方塊競技場</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background:
                linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            color: white;
            overflow-x: hidden;
        }

        /* 導航欄 */
        .navbar {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1rem 2rem;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 800;
            color: white;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-links a:hover {
            color: #f093fb;
        }

        .login-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        /* 英雄區域 */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 0 2rem;
            position: relative;
        }

        .hero-content {
            max-width: 800px;
            z-index: 2;
        }

        .hero-title {
            font-size: 4rem;
            font-weight: 900;
            margin-bottom: 1rem;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            line-height: 1.1;
        }

        .hero-subtitle {
            font-size: 1.5rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            font-weight: 400;
        }

        .hero-description {
            font-size: 1.1rem;
            margin-bottom: 3rem;
            opacity: 0.8;
            line-height: 1.6;
        }

        .cta-buttons {
            display: flex;
            gap: 1.5rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .cta-btn {
            padding: 1.25rem 2.5rem;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .cta-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .cta-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);
        }

        .cta-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .cta-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-3px);
        }

        /* 特色區域 */
        .features {
            padding: 5rem 2rem;
            background: rgba(0, 0, 0, 0.1);
        }

        .features-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .features-title {
            text-align: center;
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 3rem;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 2.5rem;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1.5rem;
            display: block;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .feature-desc {
            opacity: 0.8;
            line-height: 1.6;
        }

        /* 玩法介紹區域 */
        .gameplay {
            padding: 5rem 2rem;
            background: rgba(255, 255, 255, 0.05);
        }

        .gameplay-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
        }

        .video-placeholder {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            aspect-ratio: 16/9;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .video-placeholder:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: scale(1.02);
        }

        .play-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        .gameplay-steps {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .step {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .step-number {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 700;
            flex-shrink: 0;
        }

        .step-content h3 {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .step-content p {
            opacity: 0.8;
            line-height: 1.5;
        }

        /* 數據區域 */
        .stats {
            padding: 5rem 2rem;
            background: rgba(0, 0, 0, 0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 2rem;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: #f093fb;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 1.1rem;
            opacity: 0.8;
        }

        /* 最新消息區域 */
        .news {
            padding: 5rem 2rem;
            background: rgba(255, 255, 255, 0.05);
        }

        .news-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .news-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .news-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
        }

        .news-date {
            color: #f093fb;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .news-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .news-excerpt {
            opacity: 0.8;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .news-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .news-link:hover {
            color: #f093fb;
        }

        /* 下載區域 */
        .download {
            padding: 5rem 2rem;
            background: rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .download-title {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
        }

        .download-desc {
            font-size: 1.2rem;
            opacity: 0.8;
            margin-bottom: 3rem;
        }

        .download-buttons {
            display: flex;
            gap: 2rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .download-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 200px;
        }

        .download-btn:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-3px);
        }

        .download-icon {
            font-size: 2rem;
        }

        .download-text {
            text-align: left;
        }

        .download-platform {
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
        }

        .download-subtitle {
            font-size: 0.9rem;
            opacity: 0.7;
        }

        /* 頁腳 */
        .footer {
            background: rgba(0, 0, 0, 0.3);
            padding: 3rem 2rem 1rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3,
        .footer-section h4 {
            margin-bottom: 1rem;
            color: white;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 0.5rem;
        }

        .footer-section ul li a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .footer-section ul li a:hover {
            color: #f093fb;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            opacity: 0.7;
        }

        /* 響應式 */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.2rem;
            }

            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }

            .features-title {
                font-size: 2rem;
            }

            .gameplay-content {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .download-buttons {
                flex-direction: column;
                align-items: center;
            }
        }

        /* 載入動畫 */
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 0.8s ease forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 背景動畫 */
        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1920 1080"><circle cx="300" cy="200" r="100" fill="rgba(255,255,255,0.05)" opacity="0.5"><animate attributeName="cy" values="200;100;200" dur="6s" repeatCount="indefinite"/></circle><circle cx="1600" cy="300" r="150" fill="rgba(255,255,255,0.03)" opacity="0.3"><animate attributeName="cy" values="300;150;300" dur="8s" repeatCount="indefinite"/></circle><circle cx="800" cy="800" r="200" fill="rgba(255,255,255,0.04)" opacity="0.4"><animate attributeName="cy" values="800;600;800" dur="10s" repeatCount="indefinite"/></circle></svg>');
            pointer-events: none;
            z-index: 1;
        }
    </style>
</head>
<body>
    <!-- 導航欄 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo">Block Battle Arena</div>
            <ul class="nav-links">
                <li><a href="#home">首頁</a></li>
                <li><a href="#features">特色</a></li>
                <li><a href="#gameplay">玩法</a></li>
                <li><a href="#stats">數據</a></li>
                <li><a href="#news">最新消息</a></li>
                <li><a href="#download">下載</a></li>
            </ul>
            <button class="login-btn" onclick="loginToGame()">立即遊玩</button>
        </div>
    </nav>

    <!-- 英雄區域 -->
    <section class="hero" id="home">
        <div class="hero-content fade-in">
            <h1 class="hero-title">Block Battle Arena</h1>
            <h2 class="hero-subtitle">終極方塊競技場</h2>
            <p class="hero-description">
                在這個充滿策略與技巧的競技場中，運用各種獨特的方塊技能，
                與全球玩家展開激烈對戰。每一場戰鬥都是智慧與反應的較量！
            </p>
            <div class="cta-buttons">
                <button class="cta-btn cta-primary" onclick="loginToGame()">
                    🎮 開始遊戲
                </button>
                <a href="#features" class="cta-btn cta-secondary">
                    📖 了解更多
                </a>
            </div>
        </div>
    </section>

    <!-- 特色區域 -->
    <section class="features" id="features">
        <div class="features-container">
            <h2 class="features-title fade-in">遊戲特色</h2>
            <div class="features-grid">
                <div class="feature-card fade-in">
                    <span class="feature-icon">⚡</span>
                    <h3 class="feature-title">快節奏對戰</h3>
                    <p class="feature-desc">
                        每場戰鬥都充滿緊張刺激，考驗你的反應速度和策略思維。
                    </p>
                </div>
                <div class="feature-card fade-in">
                    <span class="feature-icon">🧩</span>
                    <h3 class="feature-title">多樣方塊技能</h3>
                    <p class="feature-desc">
                        收集和掌握各種獨特的方塊技能，打造屬於你的戰鬥風格。
                    </p>
                </div>
                <div class="feature-card fade-in">
                    <span class="feature-icon">🏆</span>
                    <h3 class="feature-title">競技排名</h3>
                    <p class="feature-desc">
                        參與排位賽，提升你的競技場排名，成為頂尖的方塊戰士。
                    </p>
                </div>
                <div class="feature-card fade-in">
                    <span class="feature-icon">🎯</span>
                    <h3 class="feature-title">技能訓練</h3>
                    <p class="feature-desc">
                        在訓練模式中磨練技巧，掌握各種高級戰術和組合技。
                    </p>
                </div>
                <div class="feature-card fade-in">
                    <span class="feature-icon">🌟</span>
                    <h3 class="feature-title">成就系統</h3>
                    <p class="feature-desc">
                        完成各種挑戰和成就，解鎖新的方塊、皮膚和獎勵。
                    </p>
                </div>
                <div class="feature-card fade-in">
                    <span class="feature-icon">👥</span>
                    <h3 class="feature-title">社交功能</h3>
                    <p class="feature-desc">
                        與朋友組隊，創建自定義房間，享受多人對戰的樂趣。
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- 玩法介紹區域 -->
    <section class="gameplay" id="gameplay">
        <div class="features-container">
            <h2 class="features-title fade-in">遊戲玩法</h2>
            <div class="gameplay-content">
                <div class="gameplay-video fade-in">
                    <div class="video-placeholder">
                        <span class="play-icon">▶️</span>
                        <p>遊戲預告片</p>
                    </div>
                </div>
                <div class="gameplay-steps fade-in">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h3>選擇技能</h3>
                            <p>從多種獨特的方塊技能中選擇，打造你的戰鬥策略</p>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h3>進入戰場</h3>
                            <p>與全球玩家匹配，在競技場中展開激烈對戰</p>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h3>獲得勝利</h3>
                            <p>運用策略和技巧擊敗對手，提升排名獲得獎勵</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 遊戲數據區域 -->
    <section class="stats" id="stats">
        <div class="features-container">
            <h2 class="features-title fade-in">遊戲數據</h2>
            <div class="stats-grid">
                <div class="stat-card fade-in">
                    <div class="stat-number">50,000+</div>
                    <div class="stat-label">活躍玩家</div>
                </div>
                <div class="stat-card fade-in">
                    <div class="stat-number">1,000,000+</div>
                    <div class="stat-label">對戰場次</div>
                </div>
                <div class="stat-card fade-in">
                    <div class="stat-number">25+</div>
                    <div class="stat-label">獨特技能</div>
                </div>
                <div class="stat-card fade-in">
                    <div class="stat-number">4.8/5</div>
                    <div class="stat-label">玩家評分</div>
                </div>
            </div>
        </div>
    </section>

    <!-- 最新消息區域 -->
    <section class="news" id="news">
        <div class="features-container">
            <h2 class="features-title fade-in">最新消息</h2>
            <div class="news-grid">
                <div class="news-card fade-in">
                    <div class="news-date">2024.01.15</div>
                    <h3 class="news-title">新技能「時空扭曲」上線！</h3>
                    <p class="news-excerpt">全新的時空系技能讓戰鬥更加多變，快來體驗吧！</p>
                    <a href="#" class="news-link">閱讀更多 →</a>
                </div>
                <div class="news-card fade-in">
                    <div class="news-date">2024.01.10</div>
                    <h3 class="news-title">排位賽第三季開始</h3>
                    <p class="news-excerpt">新賽季帶來更豐富的獎勵和全新的競技場地圖。</p>
                    <a href="#" class="news-link">閱讀更多 →</a>
                </div>
                <div class="news-card fade-in">
                    <div class="news-date">2024.01.05</div>
                    <h3 class="news-title">冬季活動火熱進行中</h3>
                    <p class="news-excerpt">參與冬季挑戰，獲得限定皮膚和豐厚獎勵！</p>
                    <a href="#" class="news-link">閱讀更多 →</a>
                </div>
            </div>
        </div>
    </section>

    <!-- 下載區域 -->
    <section class="download" id="download">
        <div class="features-container">
            <div class="download-content fade-in">
                <h2 class="download-title">立即開始你的競技場之旅</h2>
                <p class="download-desc">支援多平台，隨時隨地享受方塊對戰的樂趣</p>
                <div class="download-buttons">
                    <button class="download-btn" onclick="loginToGame()">
                        <span class="download-icon">🌐</span>
                        <div class="download-text">
                            <div class="download-platform">網頁版</div>
                            <div class="download-subtitle">立即遊玩</div>
                        </div>
                    </button>
                    <button class="download-btn">
                        <span class="download-icon">📱</span>
                        <div class="download-text">
                            <div class="download-platform">手機版</div>
                            <div class="download-subtitle">即將推出</div>
                        </div>
                    </button>
                    <button class="download-btn">
                        <span class="download-icon">💻</span>
                        <div class="download-text">
                            <div class="download-platform">桌面版</div>
                            <div class="download-subtitle">即將推出</div>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- 頁腳 -->
    <footer class="footer">
        <div class="features-container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Block Battle Arena</h3>
                    <p>終極方塊競技場，挑戰你的極限！</p>
                </div>
                <div class="footer-section">
                    <h4>遊戲</h4>
                    <ul>
                        <li><a href="#features">遊戲特色</a></li>
                        <li><a href="#gameplay">玩法介紹</a></li>
                        <li><a href="#download">立即遊玩</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>社群</h4>
                    <ul>
                        <li><a href="#">Discord</a></li>
                        <li><a href="#">Facebook</a></li>
                        <li><a href="#">Twitter</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>支援</h4>
                    <ul>
                        <li><a href="#">幫助中心</a></li>
                        <li><a href="#">聯絡我們</a></li>
                        <li><a href="#">回報問題</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Block Battle Arena. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        function loginToGame() {
            // 這裡可以添加登入邏輯
            // 目前直接跳轉到主大廳
            window.location.href = 'lobby.html';
        }

        // 平滑滾動
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 載入動畫
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.fade-in');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach((entry, index) => {
                    if (entry.isIntersecting) {
                        setTimeout(() => {
                            entry.target.style.animationDelay = `${index * 0.1}s`;
                            entry.target.classList.add('fade-in');
                        }, index * 100);
                    }
                });
            });

            elements.forEach(el => observer.observe(el));
        });

        // 導航欄滾動效果
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(0, 0, 0, 0.8)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.1)';
            }
        });
    </script>
</body>
</html>
