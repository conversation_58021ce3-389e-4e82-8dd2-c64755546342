<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Block Battle Arena - 簡化選單</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            font-family: 'Arial', sans-serif;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        #game-container {
            border: 2px solid #00d4ff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
            background: #000;
        }

        .loading-screen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 2000;
        }

        .loading-text {
            font-size: 24px;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div id="game-container">
        <div class="loading-screen" id="loading-screen">
            <div class="loading-text">載入中...</div>
            <div>Block Battle Arena - 簡化選單</div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    
    <script>
        // 遊戲配置
        const SIMPLE_CONFIG = {
            SCREEN: {
                WIDTH: 1000,
                HEIGHT: 700,
                BACKGROUND_COLOR: '#1a1a2e'
            }
        };

        // 簡化主選單場景
        class SimpleMenuScene extends Phaser.Scene {
            constructor() {
                super({ key: 'SimpleMenuScene' });
            }

            create() {
                console.log('SimpleMenuScene: 創建簡化選單...');
                
                // 背景
                this.add.rectangle(
                    SIMPLE_CONFIG.SCREEN.WIDTH / 2,
                    SIMPLE_CONFIG.SCREEN.HEIGHT / 2,
                    SIMPLE_CONFIG.SCREEN.WIDTH,
                    SIMPLE_CONFIG.SCREEN.HEIGHT,
                    0x1a1a2e
                );
                
                // 標題
                this.add.text(
                    SIMPLE_CONFIG.SCREEN.WIDTH / 2,
                    150,
                    'Block Battle Arena',
                    {
                        fontSize: '48px',
                        fill: '#ffffff',
                        fontFamily: 'Arial',
                        stroke: '#00d4ff',
                        strokeThickness: 3
                    }
                ).setOrigin(0.5);
                
                // 副標題
                this.add.text(
                    SIMPLE_CONFIG.SCREEN.WIDTH / 2,
                    200,
                    '方塊競技場 - 簡化版',
                    {
                        fontSize: '24px',
                        fill: '#cccccc',
                        fontFamily: 'Arial'
                    }
                ).setOrigin(0.5);
                
                // 開始遊戲按鈕
                const startButton = this.add.rectangle(
                    SIMPLE_CONFIG.SCREEN.WIDTH / 2,
                    300,
                    250, 60,
                    0x00d4ff
                ).setInteractive();
                
                this.add.text(
                    SIMPLE_CONFIG.SCREEN.WIDTH / 2,
                    300,
                    '開始遊戲',
                    {
                        fontSize: '24px',
                        fill: '#000000',
                        fontFamily: 'Arial',
                        fontWeight: 'bold'
                    }
                ).setOrigin(0.5);
                
                // 手動反彈測試按鈕
                const bounceTestButton = this.add.rectangle(
                    SIMPLE_CONFIG.SCREEN.WIDTH / 2,
                    380,
                    250, 60,
                    0xff6b35
                ).setInteractive();
                
                this.add.text(
                    SIMPLE_CONFIG.SCREEN.WIDTH / 2,
                    380,
                    '反彈測試',
                    {
                        fontSize: '24px',
                        fill: '#000000',
                        fontFamily: 'Arial',
                        fontWeight: 'bold'
                    }
                ).setOrigin(0.5);
                
                // 完整版按鈕
                const fullVersionButton = this.add.rectangle(
                    SIMPLE_CONFIG.SCREEN.WIDTH / 2,
                    460,
                    250, 60,
                    0x00ff00
                ).setInteractive();
                
                this.add.text(
                    SIMPLE_CONFIG.SCREEN.WIDTH / 2,
                    460,
                    '完整版遊戲',
                    {
                        fontSize: '24px',
                        fill: '#000000',
                        fontFamily: 'Arial',
                        fontWeight: 'bold'
                    }
                ).setOrigin(0.5);
                
                // 按鈕事件
                startButton.on('pointerdown', () => {
                    window.location.href = 'manual-bounce-game.html';
                });
                
                bounceTestButton.on('pointerdown', () => {
                    window.location.href = 'perfect-bounce.html';
                });
                
                fullVersionButton.on('pointerdown', () => {
                    window.location.href = 'index.html';
                });
                
                // 懸停效果
                startButton.on('pointerover', () => {
                    startButton.setFillStyle(0x0099cc);
                });
                
                startButton.on('pointerout', () => {
                    startButton.setFillStyle(0x00d4ff);
                });
                
                bounceTestButton.on('pointerover', () => {
                    bounceTestButton.setFillStyle(0xcc5529);
                });
                
                bounceTestButton.on('pointerout', () => {
                    bounceTestButton.setFillStyle(0xff6b35);
                });
                
                fullVersionButton.on('pointerover', () => {
                    fullVersionButton.setFillStyle(0x00cc00);
                });
                
                fullVersionButton.on('pointerout', () => {
                    fullVersionButton.setFillStyle(0x00ff00);
                });
                
                // 控制說明
                this.add.text(
                    SIMPLE_CONFIG.SCREEN.WIDTH / 2,
                    550,
                    '遊戲說明:\n• 開始遊戲: 簡化版本，完美反彈機制\n• 反彈測試: 測試反彈物理效果\n• 完整版遊戲: 包含所有技能和功能',
                    {
                        fontSize: '16px',
                        fill: '#cccccc',
                        fontFamily: 'Arial',
                        align: 'center'
                    }
                ).setOrigin(0.5);
                
                // 版本信息
                this.add.text(
                    20, SIMPLE_CONFIG.SCREEN.HEIGHT - 30,
                    'Simple Menu v1.0',
                    {
                        fontSize: '12px',
                        fill: '#666666',
                        fontFamily: 'Arial'
                    }
                );
                
                // 隱藏載入畫面
                setTimeout(() => {
                    const loadingScreen = document.querySelector('#loading-screen');
                    if (loadingScreen) {
                        loadingScreen.style.opacity = '0';
                        setTimeout(() => {
                            loadingScreen.style.display = 'none';
                        }, 500);
                    }
                }, 1000);
                
                console.log('SimpleMenuScene: 創建完成');
            }
        }

        // 遊戲配置
        const gameConfig = {
            type: Phaser.AUTO,
            width: SIMPLE_CONFIG.SCREEN.WIDTH,
            height: SIMPLE_CONFIG.SCREEN.HEIGHT,
            parent: 'game-container',
            backgroundColor: SIMPLE_CONFIG.SCREEN.BACKGROUND_COLOR,
            scene: [SimpleMenuScene]
        };

        // 錯誤處理
        window.addEventListener('error', (event) => {
            const errorMessage = event.error ? event.error.message : 'Unknown error';
            console.error('Error:', errorMessage);
            
            const loadingScreen = document.querySelector('#loading-screen');
            if (loadingScreen) {
                loadingScreen.innerHTML = `
                    <div style="color: #ff6b35; font-size: 24px; margin-bottom: 20px;">載入失敗</div>
                    <div style="color: #cccccc; font-size: 16px; text-align: center;">
                        錯誤: ${errorMessage}<br><br>
                        <button onclick="location.reload()" style="
                            padding: 10px 20px;
                            background: #00d4ff;
                            color: white;
                            border: none;
                            border-radius: 5px;
                            cursor: pointer;
                        ">重新載入</button>
                    </div>
                `;
            }
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('初始化簡化選單...');
            
            try {
                const game = new Phaser.Game(gameConfig);
                console.log('簡化選單初始化成功');
            } catch (error) {
                console.error('選單初始化失敗:', error);
            }
        });
    </script>
</body>
</html>
