module.exports = {
  apps: [{
    name: 'block-battle-arena',
    script: 'server.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000,
      JWT_SECRET: 'your-super-secret-jwt-key-change-this-in-production-p1p2blockgame',
      MONGODB_URI: 'mongodb+srv://minyue1001:<EMAIL>/blockbattlearena?retryWrites=true&w=majority&appName=minyue',
      DOMAIN: 'p1p2blockgame.com'
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000,
      JWT_SECRET: 'your-super-secret-jwt-key-change-this-in-production-p1p2blockgame',
      MONGODB_URI: 'mongodb+srv://minyue1001:<EMAIL>/blockbattlearena?retryWrites=true&w=majority&appName=minyue',
      DOMAIN: 'p1p2blockgame.com'
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    log_date_format: 'YYYY-MM-DD HH:mm Z'
  }],

  deploy: {
    production: {
      user: 'root',
      host: '*************',
      port: '22',
      ref: 'origin/main',
      repo: '**************:username/block-battle-arena.git',
      path: '/var/www/block-battle-arena',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};
