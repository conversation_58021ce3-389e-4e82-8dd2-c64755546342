<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Block Battle Arena - Minimal Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a2e;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        #game-container {
            border: 2px solid #00d4ff;
            border-radius: 10px;
            background: #000;
            width: 1000px;
            height: 700px;
            margin: 20px auto;
        }
        
        .status {
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .success { color: #00ff00; }
        .error { color: #ff6b35; }
        .warning { color: #ffdd00; }
    </style>
</head>
<body>
    <h1>Block Battle Arena - 最小測試版</h1>
    
    <div id="status-log"></div>
    
    <div id="game-container"></div>

    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/matter-js@0.19.0/build/matter.min.js"></script>
    
    <script>
        const statusLog = document.getElementById('status-log');
        
        function addStatus(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            statusLog.appendChild(div);
            console.log(message);
        }
        
        // 檢查依賴
        addStatus('檢查依賴項...', 'info');
        
        if (typeof Phaser !== 'undefined') {
            addStatus('✓ Phaser.js 載入成功', 'success');
        } else {
            addStatus('✗ Phaser.js 載入失敗', 'error');
        }
        
        if (typeof Matter !== 'undefined') {
            addStatus('✓ Matter.js 載入成功', 'success');
        } else {
            addStatus('✗ Matter.js 載入失敗', 'error');
        }
        
        // 最小遊戲配置
        const MINIMAL_CONFIG = {
            SCREEN: {
                WIDTH: 1000,
                HEIGHT: 700,
                BACKGROUND_COLOR: '#1a1a2e'
            },
            PLAYER: {
                SIZE: 30,
                MAX_HP: 100,
                SPAWN_POSITIONS: {
                    PLAYER1: { x: 200, y: 350 },
                    PLAYER2: { x: 800, y: 350 }
                }
            }
        };
        
        // 最小場景
        class MinimalScene extends Phaser.Scene {
            constructor() {
                super({ key: 'MinimalScene' });
            }
            
            preload() {
                addStatus('MinimalScene: 預載入階段', 'info');
            }
            
            create() {
                addStatus('MinimalScene: 創建場景', 'info');
                
                try {
                    // 背景
                    this.add.rectangle(
                        MINIMAL_CONFIG.SCREEN.WIDTH / 2,
                        MINIMAL_CONFIG.SCREEN.HEIGHT / 2,
                        MINIMAL_CONFIG.SCREEN.WIDTH,
                        MINIMAL_CONFIG.SCREEN.HEIGHT,
                        0x1a1a2e
                    );
                    
                    // 標題
                    this.add.text(
                        MINIMAL_CONFIG.SCREEN.WIDTH / 2,
                        100,
                        'Block Battle Arena',
                        {
                            fontSize: '36px',
                            fill: '#ffffff',
                            fontFamily: 'Arial'
                        }
                    ).setOrigin(0.5);
                    
                    // 狀態文字
                    this.add.text(
                        MINIMAL_CONFIG.SCREEN.WIDTH / 2,
                        200,
                        '最小測試版本運行中',
                        {
                            fontSize: '20px',
                            fill: '#00ff00',
                            fontFamily: 'Arial'
                        }
                    ).setOrigin(0.5);
                    
                    // 創建兩個簡單的方塊
                    this.player1 = this.add.rectangle(
                        MINIMAL_CONFIG.PLAYER.SPAWN_POSITIONS.PLAYER1.x,
                        MINIMAL_CONFIG.PLAYER.SPAWN_POSITIONS.PLAYER1.y,
                        MINIMAL_CONFIG.PLAYER.SIZE,
                        MINIMAL_CONFIG.PLAYER.SIZE,
                        0x00d4ff
                    );
                    
                    this.player2 = this.add.rectangle(
                        MINIMAL_CONFIG.PLAYER.SPAWN_POSITIONS.PLAYER2.x,
                        MINIMAL_CONFIG.PLAYER.SPAWN_POSITIONS.PLAYER2.y,
                        MINIMAL_CONFIG.PLAYER.SIZE,
                        MINIMAL_CONFIG.PLAYER.SIZE,
                        0xff6b35
                    );
                    
                    // 添加簡單動畫
                    this.tweens.add({
                        targets: [this.player1, this.player2],
                        scaleX: 1.1,
                        scaleY: 1.1,
                        duration: 1000,
                        yoyo: true,
                        repeat: -1,
                        ease: 'Sine.easeInOut'
                    });
                    
                    // 說明文字
                    this.add.text(
                        MINIMAL_CONFIG.SCREEN.WIDTH / 2,
                        400,
                        '如果您看到兩個閃爍的方塊，表示基本系統正常運行。\n這意味著 Phaser.js 和 Matter.js 都已正確載入。',
                        {
                            fontSize: '16px',
                            fill: '#cccccc',
                            fontFamily: 'Arial',
                            align: 'center'
                        }
                    ).setOrigin(0.5);
                    
                    // 測試按鈕
                    const testButton = this.add.rectangle(
                        MINIMAL_CONFIG.SCREEN.WIDTH / 2,
                        500,
                        200, 50,
                        0x00d4ff
                    ).setInteractive();
                    
                    this.add.text(
                        MINIMAL_CONFIG.SCREEN.WIDTH / 2,
                        500,
                        '測試完整版',
                        {
                            fontSize: '16px',
                            fill: '#000000',
                            fontFamily: 'Arial'
                        }
                    ).setOrigin(0.5);
                    
                    testButton.on('pointerdown', () => {
                        addStatus('嘗試載入完整版...', 'warning');
                        window.location.href = 'index.html';
                    });
                    
                    addStatus('✓ MinimalScene 創建成功', 'success');
                    
                } catch (error) {
                    addStatus(`✗ MinimalScene 創建失敗: ${error.message}`, 'error');
                    console.error(error);
                }
            }
            
            update() {
                // 簡單的更新邏輯
            }
        }
        
        // 遊戲配置
        const gameConfig = {
            type: Phaser.AUTO,
            width: MINIMAL_CONFIG.SCREEN.WIDTH,
            height: MINIMAL_CONFIG.SCREEN.HEIGHT,
            parent: 'game-container',
            backgroundColor: MINIMAL_CONFIG.SCREEN.BACKGROUND_COLOR,
            scene: [MinimalScene],
            physics: {
                default: 'matter',
                matter: {
                    gravity: { x: 0, y: 0 },
                    debug: false
                }
            }
        };
        
        // 錯誤處理
        window.addEventListener('error', (event) => {
            addStatus(`全域錯誤: ${event.error.message}`, 'error');
            console.error(event.error);
        });
        
        // 初始化遊戲
        addStatus('初始化最小遊戲...', 'info');
        
        try {
            const game = new Phaser.Game(gameConfig);
            addStatus('✓ 最小遊戲初始化成功', 'success');
            
            // 隱藏載入畫面（如果存在）
            setTimeout(() => {
                const loadingScreen = document.querySelector('#loading-screen');
                if (loadingScreen) {
                    loadingScreen.style.display = 'none';
                }
            }, 1000);
            
        } catch (error) {
            addStatus(`✗ 遊戲初始化失敗: ${error.message}`, 'error');
            console.error(error);
        }
    </script>
</body>
</html>
