<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Block Battle Arena - Simple Version</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            font-family: 'Arial', sans-serif;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        #game-container {
            border: 2px solid #00d4ff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
            background: #000;
        }

        .loading-screen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 2000;
        }

        .loading-text {
            font-size: 24px;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div id="game-container">
        <div class="loading-screen" id="loading-screen">
            <div class="loading-text">載入中...</div>
            <div>Block Battle Arena</div>
        </div>
    </div>

    <!-- 遊戲腳本 -->
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/matter-js@0.19.0/build/matter.min.js"></script>
    
    <script>
        // 簡化的遊戲配置
        const SIMPLE_CONFIG = {
            SCREEN: {
                WIDTH: 1000,
                HEIGHT: 700,
                BACKGROUND_COLOR: '#1a1a2e'
            }
        };

        // 簡化的主選單場景
        class SimpleMenuScene extends Phaser.Scene {
            constructor() {
                super({ key: 'SimpleMenuScene' });
            }

            create() {
                console.log('SimpleMenuScene: Creating menu...');
                
                // 隱藏載入畫面
                setTimeout(() => {
                    const loadingScreen = document.querySelector('#loading-screen');
                    if (loadingScreen) {
                        loadingScreen.style.opacity = '0';
                        setTimeout(() => {
                            loadingScreen.style.display = 'none';
                        }, 500);
                    }
                }, 1000);
                
                // 背景
                this.add.rectangle(
                    SIMPLE_CONFIG.SCREEN.WIDTH / 2,
                    SIMPLE_CONFIG.SCREEN.HEIGHT / 2,
                    SIMPLE_CONFIG.SCREEN.WIDTH,
                    SIMPLE_CONFIG.SCREEN.HEIGHT,
                    0x1a1a2e
                );
                
                // 標題
                this.add.text(
                    SIMPLE_CONFIG.SCREEN.WIDTH / 2,
                    150,
                    'Block Battle Arena',
                    {
                        fontSize: '48px',
                        fill: '#ffffff',
                        fontFamily: 'Arial',
                        stroke: '#00d4ff',
                        strokeThickness: 3
                    }
                ).setOrigin(0.5);
                
                // 副標題
                this.add.text(
                    SIMPLE_CONFIG.SCREEN.WIDTH / 2,
                    200,
                    '方塊競技場 - 簡化版',
                    {
                        fontSize: '24px',
                        fill: '#cccccc',
                        fontFamily: 'Arial'
                    }
                ).setOrigin(0.5);
                
                // 狀態信息
                this.add.text(
                    SIMPLE_CONFIG.SCREEN.WIDTH / 2,
                    300,
                    '遊戲已成功載入！',
                    {
                        fontSize: '20px',
                        fill: '#00ff00',
                        fontFamily: 'Arial'
                    }
                ).setOrigin(0.5);
                
                // 說明文字
                this.add.text(
                    SIMPLE_CONFIG.SCREEN.WIDTH / 2,
                    400,
                    '如果您看到這個畫面，表示基本系統運行正常。\n請檢查瀏覽器控制台是否有錯誤信息。',
                    {
                        fontSize: '16px',
                        fill: '#cccccc',
                        fontFamily: 'Arial',
                        align: 'center'
                    }
                ).setOrigin(0.5);
                
                // 重新載入按鈕
                const reloadButton = this.add.rectangle(
                    SIMPLE_CONFIG.SCREEN.WIDTH / 2,
                    500,
                    200, 50,
                    0x00d4ff
                ).setInteractive();
                
                this.add.text(
                    SIMPLE_CONFIG.SCREEN.WIDTH / 2,
                    500,
                    '重新載入完整版',
                    {
                        fontSize: '16px',
                        fill: '#000000',
                        fontFamily: 'Arial'
                    }
                ).setOrigin(0.5);
                
                reloadButton.on('pointerdown', () => {
                    window.location.href = 'index.html';
                });
                
                reloadButton.on('pointerover', () => {
                    reloadButton.setFillStyle(0x0099cc);
                });
                
                reloadButton.on('pointerout', () => {
                    reloadButton.setFillStyle(0x00d4ff);
                });
                
                console.log('SimpleMenuScene: Menu created successfully');
            }
        }

        // 簡化的遊戲配置
        const gameConfig = {
            type: Phaser.AUTO,
            width: SIMPLE_CONFIG.SCREEN.WIDTH,
            height: SIMPLE_CONFIG.SCREEN.HEIGHT,
            parent: 'game-container',
            backgroundColor: SIMPLE_CONFIG.SCREEN.BACKGROUND_COLOR,
            scene: [SimpleMenuScene],
            physics: {
                default: 'matter',
                matter: {
                    gravity: { x: 0, y: 0 },
                    debug: false
                }
            }
        };

        // 錯誤處理
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
        });

        // 初始化遊戲
        console.log('Initializing simple version...');
        
        try {
            const game = new Phaser.Game(gameConfig);
            console.log('Simple game initialized successfully');
        } catch (error) {
            console.error('Game initialization failed:', error);
            
            // 顯示錯誤信息
            const loadingScreen = document.querySelector('#loading-screen');
            if (loadingScreen) {
                loadingScreen.innerHTML = `
                    <div style="color: #ff6b35; font-size: 24px; margin-bottom: 20px;">載入失敗</div>
                    <div style="color: #cccccc; font-size: 16px; text-align: center;">
                        錯誤信息: ${error.message}<br><br>
                        請檢查瀏覽器控制台獲取更多信息
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
