# Block Battle Arena - 部署指南

本指南將幫助您將 Block Battle Arena 部署到生產服務器。

## 🚀 服務器信息

- **服務器 IP**: *************
- **域名**: p1p2blockgame.com
- **操作系統**: Ubuntu 20.04 LTS
- **Node.js**: v18.x
- **數據庫**: MongoDB Atlas

## 📋 部署前準備

### 1. 域名設置
確保域名 `p1p2blockgame.com` 已指向服務器 IP `*************`：

```bash
# 檢查 DNS 解析
nslookup p1p2blockgame.com
dig p1p2blockgame.com
```

### 2. SSH 訪問
確保可以 SSH 連接到服務器：

```bash
ssh root@*************
```

## 🔧 服務器初始設置

### 1. 運行服務器設置腳本

```bash
# 上傳設置腳本到服務器
scp server-setup.sh root@*************:~/

# 連接到服務器
ssh root@*************

# 運行設置腳本
chmod +x server-setup.sh
./server-setup.sh
```

### 2. 設置 SSL 證書

```bash
# 在服務器上運行
sudo certbot --nginx -d p1p2blockgame.com -d www.p1p2blockgame.com
```

## 🚀 應用部署

### 1. 自動部署（推薦）

```bash
# 在本地運行
chmod +x deploy.sh
./deploy.sh
```

### 2. 手動部署

```bash
# 1. 上傳文件
scp -r ./* root@*************:/var/www/block-battle-arena/

# 2. 連接到服務器
ssh root@*************

# 3. 進入項目目錄
cd /var/www/block-battle-arena

# 4. 安裝依賴
npm install

# 5. 啟動應用
pm2 start ecosystem.config.js --env production
pm2 save
```

## 📊 監控和管理

### PM2 管理命令

```bash
# 查看應用狀態
pm2 status

# 查看日誌
pm2 logs block-battle-arena

# 重啟應用
pm2 restart block-battle-arena

# 停止應用
pm2 stop block-battle-arena

# 查看詳細信息
pm2 show block-battle-arena

# 監控資源使用
pm2 monit
```

### Nginx 管理命令

```bash
# 檢查配置
sudo nginx -t

# 重啟 Nginx
sudo systemctl restart nginx

# 查看狀態
sudo systemctl status nginx

# 查看日誌
sudo tail -f /var/log/nginx/p1p2blockgame.com.access.log
sudo tail -f /var/log/nginx/p1p2blockgame.com.error.log
```

### 系統監控

```bash
# 查看系統資源
htop
free -h
df -h

# 查看網路連接
netstat -tulpn | grep :3000
netstat -tulpn | grep :80
netstat -tulpn | grep :443
```

## 🔒 安全配置

### 1. 防火牆設置

```bash
# 查看防火牆狀態
sudo ufw status

# 允許必要端口
sudo ufw allow 22/tcp   # SSH
sudo ufw allow 80/tcp   # HTTP
sudo ufw allow 443/tcp  # HTTPS
sudo ufw allow 3000/tcp # Node.js (可選，通過 Nginx 代理)
```

### 2. SSL 證書自動更新

```bash
# 測試自動更新
sudo certbot renew --dry-run

# 設置自動更新 cron job
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

## 🌐 訪問地址

部署完成後，可以通過以下地址訪問：

- **主頁**: https://p1p2blockgame.com
- **登入**: https://p1p2blockgame.com/login
- **註冊**: https://p1p2blockgame.com/register
- **遊戲大廳**: https://p1p2blockgame.com/lobby

## 🔄 更新部署

### 快速更新

```bash
# 在本地運行
./deploy.sh
```

### 手動更新

```bash
# 1. 上傳新文件
scp -r ./* root@*************:/var/www/block-battle-arena/

# 2. 在服務器上重啟
ssh root@************* "cd /var/www/block-battle-arena && pm2 restart block-battle-arena"
```

## 🐛 故障排除

### 常見問題

1. **應用無法啟動**
   ```bash
   # 查看 PM2 日誌
   pm2 logs block-battle-arena
   
   # 檢查 Node.js 版本
   node --version
   ```

2. **數據庫連接失敗**
   ```bash
   # 檢查環境變數
   pm2 show block-battle-arena
   
   # 測試 MongoDB 連接
   node -e "console.log(process.env.MONGODB_URI)"
   ```

3. **Nginx 502 錯誤**
   ```bash
   # 檢查 Node.js 應用是否運行
   pm2 status
   
   # 檢查端口是否監聽
   netstat -tulpn | grep :3000
   
   # 查看 Nginx 錯誤日誌
   sudo tail -f /var/log/nginx/error.log
   ```

4. **SSL 證書問題**
   ```bash
   # 檢查證書狀態
   sudo certbot certificates
   
   # 手動更新證書
   sudo certbot renew
   ```

## 📞 支援

如遇到部署問題，請檢查：

1. 服務器日誌：`pm2 logs`
2. Nginx 日誌：`/var/log/nginx/`
3. 系統日誌：`journalctl -u nginx`

---

**Block Battle Arena Team** © 2024
