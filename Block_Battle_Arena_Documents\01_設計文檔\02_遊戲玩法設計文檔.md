# 遊戲玩法設計文檔
## Block Battle Arena - Gameplay Design Document

---

## 📋 文檔資訊
- **版本號**: v1.0
- **創建日期**: 2025年6月2日
- **關聯文檔**: 總體設計文檔、技能系統設計文檔

---

## 🎮 核心玩法機制

### 基本遊戲流程
1. **遊戲開始**: 雙方方塊在場地兩端生成
2. **初始衝刺**: 自動朝對方移動開始對戰
3. **戰鬥階段**: 玩家切換模式並釋放技能
4. **勝負判定**: 一方血量歸零，遊戲結束
5. **結算畫面**: 顯示戰鬥統計和獎勵

### 物理運動系統

#### 基礎移動規則
- **恆定動能**: 方塊始終保持運動，速度恆定
- **完美彈性**: 碰撞無能量損失，100%彈射
- **無摩擦力**: 在光滑表面上滑行
- **邊界反彈**: 碰到牆壁完全反彈

#### 移動速度設定
- **基礎速度**: 200 像素/秒
- **最大速度**: 400 像素/秒 (技能增強)
- **最小速度**: 100 像素/秒 (技能減速)

#### 碰撞物理
```
碰撞後速度計算:
v1' = ((m1-m2)*v1 + 2*m2*v2) / (m1+m2)
v2' = ((m2-m1)*v2 + 2*m1*v1) / (m1+m2)

其中:
- m1, m2: 兩方塊質量 (預設相等)
- v1, v2: 碰撞前速度
- v1', v2': 碰撞後速度
```

---

## 🎯 戰鬥模式系統

### 進攻模式 (Attack Mode)
**行為特徵**:
- 主動追蹤對手位置
- 移動軌跡趨向對手
- 轉向反應速度提升

**數值效果**:
- 技能冷卻時間 -20%
- 移動速度 +10%
- 碰撞傷害 +5 HP

**視覺表現**:
- 方塊邊緣發出紅色光芒
- 移動軌跡帶有攻擊性粒子特效
- 眼神鎖定對手 (如果有眼睛設計)

### 防守模式 (Defense Mode)
**行為特徵**:
- 主動遠離對手
- 朝場地邊緣或角落移動
- 優先避開衝突

**數值效果**:
- 受到傷害 -20%
- 技能護盾效果 +25%
- 閃避成功率 +15%

**視覺表現**:
- 方塊邊緣發出藍色光芒
- 移動軌跡帶有防禦性護盾特效
- 整體略微收縮顯示防守姿態

### 中性模式 (Neutral Mode)
**行為特徵**:
- 保持當前移動方向
- 不主動追擊或躲避
- 純隨機反彈移動

**數值效果**:
- 所有屬性標準值
- 無特殊加成或減益
- 平衡的戰鬥表現

**視覺表現**:
- 標準的方塊外觀
- 基礎的移動軌跡
- 中性的色彩表現

---

## ⚔️ 戰鬥系統設計

### 生命值系統
**基礎數值**:
- 起始生命值: 100 HP
- 最大生命值: 100 HP (不可提升)
- 生命值顯示: 血條 + 數字

**傷害來源**:
1. **技能傷害**: 15-40 HP (依技能不同)
2. **碰撞傷害**: 5-15 HP (依速度不同)
3. **環境傷害**: 10-20 HP (特殊場地)

**傷害計算公式**:
```
最終傷害 = 基礎傷害 × 攻擊力係數 × (1 - 防禦力係數) × 隨機係數(0.9-1.1)
```

### 技能釋放機制
**冷卻時間系統**:
- 每個技能獨立冷卻
- 冷卻期間技能按鈕變灰
- 視覺顯示冷卻進度條

**瞄準系統**:
- **自動瞄準**: 技能自動朝對手方向釋放
- **預測瞄準**: 預判對手移動軌跡
- **手動瞄準**: 玩家可微調角度 (高級設定)

**技能優先級**:
1. 防禦技能可以打斷攻擊技能
2. 控制技能可以打斷移動技能
3. 同時釋放時，速度快的先生效

---

## 🏆 勝負判定系統

### 標準勝利條件
1. **擊敗對手**: 對方生命值歸零
2. **對手掉落**: 對方離開場地邊界 (特定場地)
3. **時間勝利**: 時間結束時生命值較高者獲勝

### 特殊勝利條件
**完美勝利**: 
- 自己滿血獲勝
- 額外經驗值 +50%
- 特殊成就解鎖

**逆轉勝利**:
- 生命值低於20%時獲勝
- 額外經驗值 +25%
- 展示特殊勝利動畫

**技術勝利**:
- 連續5次成功閃避後獲勝
- 展示技巧大師稱號
- 特殊音效和視覺效果

---

## 🎲 遊戲模式設計

### 快速對戰模式
**遊戲設定**:
- 生命值: 50 HP
- 技能冷卻: 所有技能 -30%
- 場地: 隨機選擇小型場地
- 時間限制: 90秒

**特色機制**:
- 每30秒隨機掉落增益道具
- 技能威力提升20%
- 移動速度提升15%

### 經典對戰模式
**遊戲設定**:
- 生命值: 100 HP
- 技能冷卻: 標準數值
- 場地: 雙方各選一個，系統隨機
- 時間限制: 無限制

**特色機制**:
- 標準的競技體驗
- 完整的技能和戰術選擇
- 適合排位賽和競技比賽

### 生存挑戰模式
**遊戲設定**:
- 生命值: 200 HP
- 對手: AI敵人群
- 場地: 特殊設計的生存場地
- 目標: 存活越久越好

**特色機制**:
- 敵人數量逐漸增加
- 每波敵人有不同特性
- 可獲得臨時能力提升

### 自定義對戰模式
**可調整選項**:
- 生命值: 25-200 HP
- 技能冷卻: 50%-200%
- 移動速度: 50%-200%
- 場地重力: 標準/低重力/高重力
- 特殊規則: 各種創意玩法

---

## 🎯 進度和獎勵系統

### 經驗值系統
**經驗值來源**:
- 勝利: +100 XP
- 失敗: +25 XP
- 完美勝利: +150 XP
- 首次擊敗特定對手: +50 XP
- 使用新技能: +10 XP

**等級提升獎勵**:
- 新技能解鎖
- 自定義外觀選項
- 稱號和徽章
- 專屬場地解鎖

### 成就系統
**戰鬥成就**:
- "初出茅廬": 贏得第一場勝利
- "連勝王者": 連續勝利10場
- "逆轉大師": 從20% HP逆轉勝利
- "完美戰士": 達成10次完美勝利

**技能成就**:
- "技能大師": 成功釋放100次技能
- "精準打擊": 技能命中率達95%
- "防守專家": 成功格擋50次攻擊
- "控場王者": 使用控制技能影響對手100次

**收集成就**:
- "外觀收集家": 解鎖所有基礎皮膚
- "場地探索者": 在所有場地中獲勝
- "模式大師": 完成所有遊戲模式

### 排位系統
**段位劃分**:
1. **青銅** (Bronze): 0-999分
2. **白銀** (Silver): 1000-1499分
3. **黃金** (Gold): 1500-1999分
4. **白金** (Platinum): 2000-2499分
5. **鑽石** (Diamond): 2500-2999分
6. **大師** (Master): 3000+分

**積分規則**:
- 勝利: +20~30分 (依對手段位)
- 失敗: -10~20分 (依對手段位)
- 連勝獎勵: 3連勝後每勝利額外+5分
- 段位保護: 新段位後5場不掉段

---

## 🎨 用戶界面設計

### 主選單界面
**功能區塊**:
- 快速對戰按鈕 (最顯眼)
- 排位對戰入口
- 單人練習模式
- 自定義對戰房間
- 個人資料和成就
- 設定選項

**視覺設計**:
- 動態背景 (模擬方塊運動)
- 簡潔的按鈕設計
- 清晰的層級結構
- 響應式布局

### 遊戲內界面
**必要元素**:
- 雙方生命值條 (頂部明顯位置)
- 技能圖標和冷卻顯示 (底部)
- 當前模式指示器 (左下角)
- 遊戲時間 (頂部中央)

**可選元素**:
- 小地圖 (大型場地)
- 傷害數字顯示
- 技能預覽線 (輔助瞄準)
- 觀戰者聊天 (多人模式)

### 結算界面
**顯示內容**:
- 勝負結果 (大字體)
- 詳細戰鬥統計
- 經驗值獲得動畫
- 成就解鎖提示
- 再次對戰選項

---

## 📊 平衡性設計原則

### 技能平衡
**設計原則**:
- 每個技能都有明確的對策
- 高傷害技能配高冷卻或高風險
- 防禦技能不能完全無敵
- 控制技能持續時間有限

**測試方法**:
- 模擬對戰數據分析
- 玩家反饋收集
- 專業測試員評估
- 社群平衡性討論

### 模式平衡
**進攻vs防守**:
- 進攻模式傷害較高但容易被預測
- 防守模式安全但缺乏主動性
- 鼓勵玩家動態切換模式

**場地平衡**:
- 不同場地適合不同戰術
- 避免某個場地過於偏向某種玩法
- 提供多樣化的戰鬥體驗

---

## 🔧 可調整參數

### 核心數值
```javascript
const GAME_CONFIG = {
  PLAYER_HP: 100,
  BASE_SPEED: 200,
  COLLISION_DAMAGE: {min: 5, max: 15},
  SKILL_COOLDOWN_MODIFIER: 1.0,
  ATTACK_MODE_BONUS: 0.2,
  DEFENSE_MODE_REDUCTION: 0.2
};
```

### 技能數值
```javascript
const SKILL_CONFIG = {
  SHOCKWAVE: {damage: 25, cooldown: 3000, range: 300},
  SHIELD: {duration: 5000, cooldown: 8000, absorption: 100},
  TELEPORT: {range: 200, cooldown: 8000, invulnerability: 500}
};
```

### 場地參數
```javascript
const ARENA_CONFIG = {
  STANDARD: {width: 800, height: 600, friction: 0},
  LARGE: {width: 1200, height: 800, friction: 0},
  GRAVITY_WELLS: {count: 2, strength: 50}
};
```

---

*此文檔將根據測試結果和玩家反饋持續更新和優化*