#!/bin/bash

# Block Battle Arena 部署腳本
# 用於部署到 ************* 服務器

echo "=========================================="
echo "Block Battle Arena - 部署腳本"
echo "=========================================="
echo ""

# 設定變數
SERVER_IP="*************"
SERVER_USER="root"
PROJECT_NAME="block-battle-arena"
PROJECT_PATH="/var/www/$PROJECT_NAME"
DOMAIN="p1p2blockgame.com"

echo "🚀 開始部署到服務器..."
echo "服務器: $SERVER_IP"
echo "域名: $DOMAIN"
echo "路徑: $PROJECT_PATH"
echo ""

# 1. 連接服務器並創建目錄
echo "📁 創建項目目錄..."
ssh $SERVER_USER@$SERVER_IP "mkdir -p $PROJECT_PATH"
ssh $SERVER_USER@$SERVER_IP "mkdir -p $PROJECT_PATH/logs"

# 2. 上傳文件
echo "📤 上傳項目文件..."
scp -r ./* $SERVER_USER@$SERVER_IP:$PROJECT_PATH/

# 3. 在服務器上安裝依賴
echo "📦 安裝 Node.js 依賴..."
ssh $SERVER_USER@$SERVER_IP "cd $PROJECT_PATH && npm install"

# 4. 安裝 PM2 (如果未安裝)
echo "🔧 檢查並安裝 PM2..."
ssh $SERVER_USER@$SERVER_IP "npm list -g pm2 || npm install -g pm2"

# 5. 停止舊的進程
echo "🛑 停止舊的進程..."
ssh $SERVER_USER@$SERVER_IP "cd $PROJECT_PATH && pm2 stop $PROJECT_NAME || true"
ssh $SERVER_USER@$SERVER_IP "cd $PROJECT_PATH && pm2 delete $PROJECT_NAME || true"

# 6. 啟動新的進程
echo "🚀 啟動新的進程..."
ssh $SERVER_USER@$SERVER_IP "cd $PROJECT_PATH && pm2 start ecosystem.config.js --env production"

# 7. 保存 PM2 配置
echo "💾 保存 PM2 配置..."
ssh $SERVER_USER@$SERVER_IP "pm2 save"
ssh $SERVER_USER@$SERVER_IP "pm2 startup"

# 8. 檢查狀態
echo "📊 檢查部署狀態..."
ssh $SERVER_USER@$SERVER_IP "pm2 status"

echo ""
echo "✅ 部署完成！"
echo ""
echo "🌐 訪問地址:"
echo "   http://$DOMAIN"
echo "   http://$SERVER_IP:3000"
echo ""
echo "🔧 管理命令:"
echo "   查看狀態: ssh $SERVER_USER@$SERVER_IP 'pm2 status'"
echo "   查看日誌: ssh $SERVER_USER@$SERVER_IP 'pm2 logs $PROJECT_NAME'"
echo "   重啟應用: ssh $SERVER_USER@$SERVER_IP 'pm2 restart $PROJECT_NAME'"
echo ""
