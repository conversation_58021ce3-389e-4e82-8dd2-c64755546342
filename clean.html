<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Block Battle Arena - 乾淨版</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            font-family: 'Arial', sans-serif;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        #game-container {
            border: 2px solid #00d4ff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
            background: #000;
        }

        .loading-screen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 2000;
        }

        .loading-text {
            font-size: 24px;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div id="game-container">
        <div class="loading-screen" id="loading-screen">
            <div class="loading-text">載入中...</div>
            <div>Block Battle Arena - 乾淨版</div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/matter-js@0.19.0/build/matter.min.js"></script>
    
    <script>
        // 簡化的遊戲配置
        const CLEAN_CONFIG = {
            SCREEN: {
                WIDTH: 1000,
                HEIGHT: 700,
                BACKGROUND_COLOR: '#1a1a2e'
            },
            PLAYER: {
                SIZE: 30,
                MAX_HP: 100,
                BASE_SPEED: 200,
                SPAWN_POSITIONS: {
                    PLAYER1: { x: 200, y: 350 },
                    PLAYER2: { x: 800, y: 350 }
                }
            },
            ARENA: {
                WALL_THICKNESS: 20,
                BOUNDS: {
                    LEFT: 50,
                    RIGHT: 950,
                    TOP: 50,
                    BOTTOM: 650
                }
            }
        };

        // 簡化的場景
        class CleanGameScene extends Phaser.Scene {
            constructor() {
                super({ key: 'CleanGameScene' });
            }

            preload() {
                console.log('CleanGameScene: Preloading...');
                this.matter.world.setBounds(0, 0, CLEAN_CONFIG.SCREEN.WIDTH, CLEAN_CONFIG.SCREEN.HEIGHT);
                this.matter.world.disableGravity();
            }

            create() {
                console.log('CleanGameScene: Creating...');
                
                try {
                    // 背景
                    this.add.rectangle(
                        CLEAN_CONFIG.SCREEN.WIDTH / 2,
                        CLEAN_CONFIG.SCREEN.HEIGHT / 2,
                        CLEAN_CONFIG.SCREEN.WIDTH,
                        CLEAN_CONFIG.SCREEN.HEIGHT,
                        0x1a1a2e
                    );
                    
                    // 創建場地邊界
                    this.createArena();
                    
                    // 創建玩家
                    this.createPlayers();
                    
                    // 設置碰撞檢測
                    this.matter.world.on('collisionstart', (event) => {
                        this.handleCollisions(event);
                    });
                    
                    // 隱藏載入畫面
                    setTimeout(() => {
                        const loadingScreen = document.querySelector('#loading-screen');
                        if (loadingScreen) {
                            loadingScreen.style.opacity = '0';
                            setTimeout(() => {
                                loadingScreen.style.display = 'none';
                            }, 500);
                        }
                    }, 1000);
                    
                    console.log('CleanGameScene: Created successfully');
                    
                } catch (error) {
                    console.error('CleanGameScene creation failed:', error);
                }
            }

            createArena() {
                const bounds = CLEAN_CONFIG.ARENA.BOUNDS;
                const thickness = CLEAN_CONFIG.ARENA.WALL_THICKNESS;
                
                // 創建四面牆
                const walls = [
                    { x: CLEAN_CONFIG.SCREEN.WIDTH / 2, y: bounds.TOP - thickness/2, w: CLEAN_CONFIG.SCREEN.WIDTH, h: thickness },
                    { x: CLEAN_CONFIG.SCREEN.WIDTH / 2, y: bounds.BOTTOM + thickness/2, w: CLEAN_CONFIG.SCREEN.WIDTH, h: thickness },
                    { x: bounds.LEFT - thickness/2, y: CLEAN_CONFIG.SCREEN.HEIGHT / 2, w: thickness, h: CLEAN_CONFIG.SCREEN.HEIGHT },
                    { x: bounds.RIGHT + thickness/2, y: CLEAN_CONFIG.SCREEN.HEIGHT / 2, w: thickness, h: CLEAN_CONFIG.SCREEN.HEIGHT }
                ];
                
                this.walls = [];
                walls.forEach(wall => {
                    const wallBody = this.matter.add.rectangle(wall.x, wall.y, wall.w, wall.h, {
                        isStatic: true
                    });
                    wallBody.entityType = 'wall';
                    this.walls.push(wallBody);
                });
                
                // 視覺邊界
                const graphics = this.add.graphics();
                graphics.lineStyle(4, 0x00d4ff, 0.8);
                graphics.strokeRect(
                    bounds.LEFT - thickness/2,
                    bounds.TOP - thickness/2,
                    bounds.RIGHT - bounds.LEFT + thickness,
                    bounds.BOTTOM - bounds.TOP + thickness
                );
            }

            createPlayers() {
                const spawn1 = CLEAN_CONFIG.PLAYER.SPAWN_POSITIONS.PLAYER1;
                const spawn2 = CLEAN_CONFIG.PLAYER.SPAWN_POSITIONS.PLAYER2;
                
                // 創建玩家1
                this.player1Body = this.matter.add.rectangle(spawn1.x, spawn1.y, CLEAN_CONFIG.PLAYER.SIZE, CLEAN_CONFIG.PLAYER.SIZE, {
                    frictionAir: 0,
                    friction: 0,
                    restitution: 1.0,
                    density: 1
                });
                this.player1Body.entityType = 'player';
                this.player1Body.playerId = 1;
                
                this.player1Sprite = this.add.rectangle(spawn1.x, spawn1.y, CLEAN_CONFIG.PLAYER.SIZE, CLEAN_CONFIG.PLAYER.SIZE, 0x00d4ff);
                this.player1Sprite.setStrokeStyle(2, 0xffffff);
                
                // 創建玩家2
                this.player2Body = this.matter.add.rectangle(spawn2.x, spawn2.y, CLEAN_CONFIG.PLAYER.SIZE, CLEAN_CONFIG.PLAYER.SIZE, {
                    frictionAir: 0,
                    friction: 0,
                    restitution: 1.0,
                    density: 1
                });
                this.player2Body.entityType = 'player';
                this.player2Body.playerId = 2;
                
                this.player2Sprite = this.add.rectangle(spawn2.x, spawn2.y, CLEAN_CONFIG.PLAYER.SIZE, CLEAN_CONFIG.PLAYER.SIZE, 0xff6b35);
                this.player2Sprite.setStrokeStyle(2, 0xffffff);
                
                // 設置初始速度
                this.matter.body.setVelocity(this.player1Body, { x: 3, y: 2 });
                this.matter.body.setVelocity(this.player2Body, { x: -3, y: -2 });
                
                // 標題
                this.add.text(
                    CLEAN_CONFIG.SCREEN.WIDTH / 2,
                    100,
                    'Block Battle Arena - 乾淨版',
                    {
                        fontSize: '24px',
                        fill: '#ffffff',
                        fontFamily: 'Arial'
                    }
                ).setOrigin(0.5);
                
                this.add.text(
                    CLEAN_CONFIG.SCREEN.WIDTH / 2,
                    130,
                    '兩個方塊在永續運動中',
                    {
                        fontSize: '16px',
                        fill: '#cccccc',
                        fontFamily: 'Arial'
                    }
                ).setOrigin(0.5);
            }

            handleCollisions(event) {
                // 簡化的碰撞處理
                const pairs = event.pairs;
                
                for (let pair of pairs) {
                    const bodyA = pair.bodyA;
                    const bodyB = pair.bodyB;
                    
                    // 只記錄碰撞，不做複雜處理
                    if (bodyA.entityType === 'player' && bodyB.entityType === 'player') {
                        console.log('Player collision detected');
                    }
                }
            }

            update() {
                // 更新視覺位置
                if (this.player1Body && this.player1Sprite) {
                    this.player1Sprite.setPosition(this.player1Body.position.x, this.player1Body.position.y);
                }
                
                if (this.player2Body && this.player2Sprite) {
                    this.player2Sprite.setPosition(this.player2Body.position.x, this.player2Body.position.y);
                }
            }
        }

        // 遊戲配置
        const gameConfig = {
            type: Phaser.AUTO,
            width: CLEAN_CONFIG.SCREEN.WIDTH,
            height: CLEAN_CONFIG.SCREEN.HEIGHT,
            parent: 'game-container',
            backgroundColor: CLEAN_CONFIG.SCREEN.BACKGROUND_COLOR,
            scene: [CleanGameScene],
            physics: {
                default: 'matter',
                matter: {
                    gravity: { x: 0, y: 0 },
                    debug: false
                }
            }
        };

        // 錯誤處理
        window.addEventListener('error', (event) => {
            const errorMessage = event.error ? event.error.message : 'Unknown error';
            console.error('Error:', errorMessage);
            
            const loadingScreen = document.querySelector('#loading-screen');
            if (loadingScreen) {
                loadingScreen.innerHTML = `
                    <div style="color: #ff6b35; font-size: 24px; margin-bottom: 20px;">載入失敗</div>
                    <div style="color: #cccccc; font-size: 16px; text-align: center;">
                        錯誤: ${errorMessage}<br><br>
                        <button onclick="location.reload()" style="
                            padding: 10px 20px;
                            background: #00d4ff;
                            color: white;
                            border: none;
                            border-radius: 5px;
                            cursor: pointer;
                        ">重新載入</button>
                    </div>
                `;
            }
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Initializing clean version...');
            
            try {
                const game = new Phaser.Game(gameConfig);
                console.log('Clean game initialized successfully');
            } catch (error) {
                console.error('Game initialization failed:', error);
            }
        });
    </script>
</body>
</html>
