<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Block Battle Arena - 主大廳</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Space+Grotesk:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background:
                linear-gradient(45deg, #ff6b6b 0%, #4ecdc4 25%, #45b7d1 50%, #96ceb4 75%, #ffeaa7 100%),
                radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.2) 0%, transparent 30%),
                radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.15) 0%, transparent 40%);
            background-size: 400% 400%;
            animation: gradientShift 8s ease-in-out infinite;
            min-height: 100vh;
            color: white;
            overflow-x: hidden;
            position: relative;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 10% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 20%),
                radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.05) 0%, transparent 30%);
            pointer-events: none;
            z-index: -1;
            animation: floatingBubbles 6s ease-in-out infinite;
        }

        @keyframes floatingBubbles {
            0%, 100% { transform: translateY(0) scale(1); }
            50% { transform: translateY(-20px) scale(1.1); }
        }

        @keyframes floatingOrbs {
            0%, 100% {
                transform: translate(0, 0) scale(1);
                opacity: 0.3;
            }
            50% {
                transform: translate(20px, -20px) scale(1.1);
                opacity: 0.6;
            }
        }

        .container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 3px solid #ff6b6b;
            padding: 0 1rem;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 4px 20px rgba(255, 107, 107, 0.3);
        }

        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 70px;
        }

        .logo {
            font-family: 'Space Grotesk', sans-serif;
            font-size: 1.4rem;
            font-weight: 800;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 1.2rem;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
            animation: logoFloat 3s ease-in-out infinite;
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-3px) rotate(5deg); }
        }

        .nav-links {
            display: flex;
            gap: 1.5rem;
            list-style: none;
        }

        .nav-links a {
            color: #333;
            text-decoration: none;
            font-weight: 600;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .nav-links a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s ease;
        }

        .nav-links a:hover {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .nav-links a:hover::before {
            left: 100%;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            color: white;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
            transition: all 0.3s ease;
            border: 3px solid white;
        }

        .user-avatar:hover {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.5);
        }

        .main-content {
            flex: 1;
            max-width: 1400px;
            margin: 0 auto;
            padding: 3rem 2rem;
            width: 100%;
        }

        .hero-section {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 30px;
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: heroRotate 10s linear infinite;
        }

        @keyframes heroRotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hero-title {
            font-family: 'Space Grotesk', sans-serif;
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: titleGradient 4s ease-in-out infinite;
            position: relative;
            z-index: 1;
            text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
        }

        @keyframes titleGradient {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .hero-subtitle {
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 2rem;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.6;
            position: relative;
            z-index: 1;
            font-weight: 500;
        }

        .hero-stats {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 20px;
            padding: 1.5rem 1rem;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .stat-item:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 10px 25px rgba(255, 107, 107, 0.3);
            border-color: rgba(255, 107, 107, 0.5);
        }

        .stat-number {
            font-size: 1.8rem;
            font-weight: 800;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: block;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.8);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            font-weight: 600;
        }

        .game-modes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .mode-card {
            background: rgba(255, 255, 255, 0.95);
            border: 3px solid transparent;
            border-radius: 25px;
            padding: 2rem 1.5rem;
            backdrop-filter: blur(20px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .mode-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            z-index: -1;
            margin: -3px;
            border-radius: inherit;
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        .mode-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 15px 40px rgba(255, 107, 107, 0.3);
        }

        .mode-card:hover::before {
            opacity: 1;
        }

        .mode-card:nth-child(1) { animation-delay: 0.1s; }
        .mode-card:nth-child(2) { animation-delay: 0.2s; }
        .mode-card:nth-child(3) { animation-delay: 0.3s; }
        .mode-card:nth-child(4) { animation-delay: 0.4s; }

        .mode-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .mode-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .mode-title {
            font-family: 'Space Grotesk', sans-serif;
            font-size: 1.25rem;
            font-weight: 600;
            color: white;
        }

        .mode-description {
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .mode-stats {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .mode-stat {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 8px;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            color: #3b82f6;
        }

        .mode-action {
            width: 100%;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .mode-action:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .player-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            backdrop-filter: blur(20px);
        }

        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: bold;
            color: white;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
        }

        .player-name {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .player-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 15px;
        }

        .stat-item {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #00ffff;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #cccccc;
        }

        .battle-modes {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .mode-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 30px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(20px);
            height: 180px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .mode-card:hover {
            transform: translateY(-5px);
            border-color: rgba(255, 255, 255, 0.4);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            background: rgba(255, 255, 255, 0.15);
        }

        .mode-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.9;
        }

        .mode-title {
            font-family: 'Orbitron', monospace;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: white;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .mode-description {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.4;
            font-weight: 300;
        }

        .quick-actions {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .action-btn {
            flex: 1;
            padding: 16px 24px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
            background: linear-gradient(135deg, #7c8df0, #8a5fb8);
        }

        .action-btn.secondary {
            background: linear-gradient(135deg, #764ba2, #667eea);
            box-shadow: 0 4px 15px rgba(118, 75, 162, 0.3);
        }

        .action-btn.secondary:hover {
            box-shadow: 0 6px 20px rgba(118, 75, 162, 0.4);
            background: linear-gradient(135deg, #8a5fb8, #7c8df0);
        }

        .online-players {
            max-height: 300px;
            overflow-y: auto;
        }

        .player-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin-bottom: 10px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            border-left: 3px solid #00ffff;
        }

        .player-status {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #00ff00;
            margin-right: 10px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .player-info {
            flex: 1;
        }

        .challenge-btn {
            padding: 5px 10px;
            background: rgba(255, 51, 102, 0.8);
            border: none;
            border-radius: 5px;
            color: white;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .challenge-btn:hover {
            background: rgba(255, 51, 102, 1);
            transform: scale(1.05);
        }

        .news-item {
            background: rgba(0, 0, 0, 0.2);
            border-left: 3px solid #ff3366;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 8px;
        }

        .news-title {
            font-weight: 600;
            margin-bottom: 5px;
            color: #ff3366;
        }

        .news-content {
            font-size: 0.9rem;
            color: #cccccc;
            line-height: 1.4;
        }

        .news-time {
            font-size: 0.8rem;
            color: #888;
            margin-top: 5px;
        }

        @media (max-width: 1200px) {
            .container {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto auto auto;
            }

            .sidebar {
                order: 2;
            }

            .main-content {
                order: 1;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 導航欄 -->
        <nav class="navbar">
            <div class="nav-content">
                <a href="#" class="logo">
                    <div class="logo-icon">B</div>
                    Block Battle Arena
                </a>

                <ul class="nav-links">
                    <li><a href="#home">首頁</a></li>
                    <li><a href="#leaderboard">排行榜</a></li>
                    <li><a href="#tournaments">錦標賽</a></li>
                    <li><a href="#community">社群</a></li>
                </ul>

                <div class="user-menu">
                    <div class="user-avatar">P1</div>
                </div>
            </div>
        </nav>

        <!-- 主要內容 -->
        <main class="main-content">
            <!-- 英雄區域 -->
            <section class="hero-section">
                <h1 class="hero-title">Block Battle Arena</h1>
                <p class="hero-subtitle">
                    體驗最激烈的方塊競技場戰鬥！與全球玩家對戰，展現你的技能，成為終極冠軍。
                </p>

                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number">1,247</span>
                        <span class="stat-label">在線玩家</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">8,932</span>
                        <span class="stat-label">今日對戰</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">156</span>
                        <span class="stat-label">你的排名</span>
                    </div>
                </div>
            </section>

            <!-- 遊戲模式 -->
            <section class="game-modes">
                <div class="mode-card" onclick="startQuickMatch()">
                    <div class="mode-header">
                        <div class="mode-icon">⚡</div>
                        <div class="mode-title">快速對戰</div>
                    </div>
                    <div class="mode-description">
                        立即匹配對手，開始激烈的1v1戰鬥。快速進入遊戲，測試你的技能。
                    </div>
                    <div class="mode-stats">
                        <span class="mode-stat">平均等待: 30秒</span>
                        <span class="mode-stat">難度: 中等</span>
                    </div>
                    <button class="mode-action">開始對戰</button>
                </div>

                <div class="mode-card" onclick="startRankedMatch()">
                    <div class="mode-header">
                        <div class="mode-icon">👑</div>
                        <div class="mode-title">排位賽</div>
                    </div>
                    <div class="mode-description">
                        參與排位賽，提升你的競技場排名。每場勝利都會讓你更接近頂峰。
                    </div>
                    <div class="mode-stats">
                        <span class="mode-stat">賽季獎勵</span>
                        <span class="mode-stat">排名系統</span>
                    </div>
                    <button class="mode-action">進入排位</button>
                </div>

                <div class="mode-card" onclick="createCustomRoom()">
                    <div class="mode-header">
                        <div class="mode-icon">🎯</div>
                        <div class="mode-title">自定義房間</div>
                    </div>
                    <div class="mode-description">
                        創建私人房間，邀請朋友一起對戰。自定義規則，享受專屬遊戲體驗。
                    </div>
                    <div class="mode-stats">
                        <span class="mode-stat">私人房間</span>
                        <span class="mode-stat">自定義規則</span>
                    </div>
                    <button class="mode-action">創建房間</button>
                </div>

                <div class="mode-card" onclick="openSkillSelection()">
                    <div class="mode-header">
                        <div class="mode-icon">🔥</div>
                        <div class="mode-title">技能訓練</div>
                    </div>
                    <div class="mode-description">
                        練習技能組合，完善你的戰術。在安全環境中測試新策略。
                    </div>
                    <div class="mode-stats">
                        <span class="mode-stat">技能練習</span>
                        <span class="mode-stat">戰術測試</span>
                    </div>
                    <button class="mode-action">開始訓練</button>
                </div>
            </section>
        </main>
    </div>

    <script>
        // 遊戲功能
        function startQuickMatch() {
            console.log('開始快速對戰');
            window.location.href = 'index.html';
        }

        function startRankedMatch() {
            console.log('開始排位賽');
            alert('排位賽功能即將推出！');
        }

        function createCustomRoom() {
            console.log('創建自定義房間');
            alert('自定義房間功能即將推出！');
        }

        function openSkillSelection() {
            console.log('打開技能選擇');
            window.location.href = 'index.html';
        }

        function startGame() {
            console.log('開始遊戲');
            window.location.href = 'index.html';
        }

        function openSettings() {
            console.log('打開設置');
            alert('設置功能即將推出！');
        }

        function challengePlayer(playerName) {
            console.log(`挑戰玩家: ${playerName}`);
            if (confirm(`確定要挑戰 ${playerName} 嗎？`)) {
                alert(`正在向 ${playerName} 發送挑戰邀請...`);
                // 這裡可以添加實際的挑戰邏輯
            }
        }

        // 動態更新在線玩家數據
        function updateOnlinePlayers() {
            // 模擬動態數據更新
            const players = document.querySelectorAll('.player-item');
            players.forEach(player => {
                const status = player.querySelector('.player-status');
                // 隨機改變在線狀態顏色
                if (Math.random() > 0.8) {
                    status.style.background = Math.random() > 0.5 ? '#ffaa00' : '#00ff00';
                }
            });
        }

        // 每5秒更新一次在線玩家狀態
        setInterval(updateOnlinePlayers, 5000);

        // 頁面載入完成後的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Block Battle Arena 主大廳已載入');

            // 添加一些動畫效果
            const cards = document.querySelectorAll('.mode-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.style.animation = 'fadeInUp 0.6s ease forwards';
            });
        });

        // CSS 動畫
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
