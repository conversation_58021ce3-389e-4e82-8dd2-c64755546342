<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Block Battle Arena - 主大廳</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background:
                linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6)),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1920 1080"><defs><linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%23667eea;stop-opacity:1" /><stop offset="50%" style="stop-color:%23764ba2;stop-opacity:1" /><stop offset="100%" style="stop-color:%23f093fb;stop-opacity:1" /></linearGradient></defs><rect width="1920" height="1080" fill="url(%23bg)"/><circle cx="300" cy="200" r="100" fill="rgba(255,255,255,0.1)"/><circle cx="1600" cy="300" r="150" fill="rgba(255,255,255,0.05)"/><circle cx="800" cy="800" r="200" fill="rgba(255,255,255,0.08)"/><polygon points="100,900 200,700 300,900" fill="rgba(255,255,255,0.06)"/><polygon points="1500,100 1700,50 1650,250" fill="rgba(255,255,255,0.04)"/></svg>');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            min-height: 100vh;
            color: white;
            overflow-x: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            width: 100vw;
            height: 100vh;
            padding: 2rem;
            display: grid;
            grid-template-areas:
                "player-info currency top-actions"
                ". . ."
                ". . ."
                "bottom-nav bottom-nav start-btn";
            grid-template-columns: 300px 1fr 300px;
            grid-template-rows: auto 1fr 1fr auto;
            gap: 2rem;
        }

        /* 左上角 - 玩家信息 */
        .player-info {
            grid-area: player-info;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            gap: 1rem;
            height: fit-content;
        }

        .player-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
        }

        .player-details h3 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .player-exp {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .exp-bar {
            width: 150px;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            margin-top: 0.5rem;
            overflow: hidden;
        }

        .exp-progress {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 65%;
            border-radius: 3px;
        }

        /* 中上方 - 貨幣區域 */
        .currency {
            grid-area: currency;
            display: flex;
            justify-content: center;
            gap: 2rem;
            height: fit-content;
        }

        .currency-item {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 12px;
            padding: 1rem 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            gap: 0.75rem;
            min-width: 120px;
        }

        .currency-icon {
            font-size: 1.5rem;
        }

        .currency-amount {
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
        }

        /* 右上角 - 頂部操作 */
        .top-actions {
            grid-area: top-actions;
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            height: fit-content;
        }

        .action-icon {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .action-icon:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: scale(1.1);
        }

        /* 底部導覽列 */
        .bottom-nav {
            grid-area: bottom-nav;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1rem 2rem;
            display: flex;
            justify-content: center;
            gap: 4rem;
            align-items: center;
            margin: 0 auto;
            width: fit-content;
        }

        .nav-item {
            background: none;
            border: none;
            color: white;
            font-size: 2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 1rem;
            border-radius: 12px;
            min-width: 80px;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-3px);
        }

        .nav-label {
            font-size: 0.8rem;
            font-weight: 500;
            opacity: 0.9;
        }

        /* 右下角 - 開始按鈕 */
        .start-btn {
            grid-area: start-btn;
            display: flex;
            justify-content: flex-end;
            align-items: flex-end;
        }

        .start-game-btn {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 2.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .start-game-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);
        }

        /* 響應式 */
        @media (max-width: 768px) {
            .container {
                grid-template-areas:
                    "player-info"
                    "currency"
                    "top-actions"
                    "bottom-nav"
                    "start-btn";
                grid-template-columns: 1fr;
                grid-template-rows: auto auto auto auto auto;
                padding: 1rem;
                gap: 1rem;
            }

            .player-info, .currency, .top-actions {
                justify-self: center;
            }

            .bottom-nav {
                gap: 2rem;
            }

            .nav-item {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }

            .start-game-btn {
                width: 100px;
                height: 100px;
                font-size: 2rem;
            }
        }

        /* 載入動畫 */
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 0.8s ease forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左上角 - 玩家信息 -->
        <div class="player-info fade-in">
            <div class="player-avatar">P1</div>
            <div class="player-details">
                <h3>玩家001</h3>
                <div class="player-exp">等級 15</div>
                <div class="exp-bar">
                    <div class="exp-progress"></div>
                </div>
            </div>
        </div>

        <!-- 中上方 - 貨幣 -->
        <div class="currency fade-in">
            <div class="currency-item">
                <span class="currency-icon">💰</span>
                <span class="currency-amount">1,250</span>
            </div>
            <div class="currency-item">
                <span class="currency-icon">💎</span>
                <span class="currency-amount">45</span>
            </div>
        </div>

        <!-- 右上角 - 頂部操作 -->
        <div class="top-actions fade-in">
            <button class="action-icon" onclick="openSettings()">⚙️</button>
            <button class="action-icon" onclick="openProfile()">👤</button>
        </div>

        <!-- 底部導覽列 -->
        <div class="bottom-nav fade-in">
            <button class="nav-item" onclick="openShop()">
                <span>🛒</span>
                <span class="nav-label">商店</span>
            </button>
            <button class="nav-item" onclick="openMissions()">
                <span>📋</span>
                <span class="nav-label">任務</span>
            </button>
            <button class="nav-item" onclick="openInventory()">
                <span>🧩</span>
                <span class="nav-label">庫存</span>
            </button>
        </div>

        <!-- 右下角 - 開始按鈕 -->
        <div class="start-btn fade-in">
            <button class="start-game-btn" onclick="startGame()">⚡</button>
        </div>
    </div>

    <script>
        function startGame() {
            window.location.href = 'index.html';
        }

        function openSettings() {
            alert('設置功能即將推出！');
        }

        function openProfile() {
            alert('個人資料功能即將推出！');
        }

        function openShop() {
            alert('商店功能即將推出！');
        }

        function openMissions() {
            alert('任務系統即將推出！');
        }

        function openInventory() {
            alert('方塊庫存功能即將推出！');
        }

        // 載入動畫
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.fade-in');
            elements.forEach((el, index) => {
                el.style.animationDelay = `${index * 0.1}s`;
            });
        });
    </script>
</body>
</html>
