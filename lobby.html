<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Block Battle Arena - 主大廳</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 頭部 */
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem 0;
        }

        .logo {
            font-size: 3.5rem;
            font-weight: 800;
            color: white;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 400;
        }

        /* 主要內容 */
        .main-content {
            flex: 1;
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 3rem;
        }

        .game-container {
            width: 100%;
            min-height: 100vh;
            position: relative;
            overflow: hidden;
            display: grid;
            grid-template-areas:
                "profile settings"
                "other-functions battle";
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 2rem;
            padding: 2rem;
        }

        /* 左上角 - 個人資料 */
        .profile-section {
            grid-area: profile;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 800;
            font-size: 3rem;
            color: white;
            border: 5px solid white;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            margin-bottom: 1.5rem;
        }

        .profile-name {
            font-family: 'Fredoka One', cursive;
            font-size: 2rem;
            color: #333;
            margin-bottom: 1rem;
            text-align: center;
        }

        .level-info {
            width: 100%;
            margin-bottom: 1rem;
        }

        .level-text {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }

        .level-bar {
            width: 100%;
            height: 12px;
            background: #e0e0e0;
            border-radius: 6px;
            overflow: hidden;
            position: relative;
        }

        .level-progress {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 6px;
            width: 65%;
            transition: width 0.3s ease;
        }

        /* 右上角 - 設定區域 */
        .settings-section {
            grid-area: settings;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 1.5rem;
        }

        .settings-title {
            font-family: 'Fredoka One', cursive;
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 1rem;
        }

        .settings-btn {
            width: 100%;
            padding: 1rem 2rem;
            border: none;
            border-radius: 15px;
            font-family: 'Fredoka One', cursive;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .settings-btn.primary {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
        }

        .settings-btn.danger {
            background: linear-gradient(135deg, #fd79a8, #e84393);
            color: white;
        }

        .settings-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        /* 左下角 - 其他功能 */
        .other-functions-section {
            grid-area: other-functions;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .functions-title {
            font-family: 'Fredoka One', cursive;
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 1.5rem;
        }

        .functions-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            width: 100%;
        }

        .function-btn {
            background: linear-gradient(135deg, #00b894, #00cec9);
            border: none;
            border-radius: 15px;
            padding: 1.5rem 1rem;
            color: white;
            font-family: 'Fredoka One', cursive;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
        }

        .function-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 184, 148, 0.4);
        }

        .function-icon {
            font-size: 2rem;
        }

        /* 右下角 - 對戰區域 */
        .battle-section {
            grid-area: battle;
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            border-radius: 25px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .battle-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: battleGlow 4s ease-in-out infinite;
        }

        @keyframes battleGlow {
            0%, 100% { transform: translate(0, 0) scale(1); }
            50% { transform: translate(-20px, -20px) scale(1.1); }
        }

        .battle-title {
            font-family: 'Fredoka One', cursive;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            position: relative;
            z-index: 1;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .battle-subtitle {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
            text-align: center;
            opacity: 0.9;
        }

        .battle-btn {
            background: rgba(255, 255, 255, 0.9);
            color: #ff6b6b;
            border: none;
            border-radius: 20px;
            padding: 1.5rem 3rem;
            font-family: 'Fredoka One', cursive;
            font-size: 1.3rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
            position: relative;
            z-index: 1;
        }

        .battle-btn:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .welcome-section {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            border-radius: 20px;
            padding: 2rem 1.5rem;
            margin-bottom: 1.5rem;
            color: white;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .welcome-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: welcomeFloat 6s ease-in-out infinite;
        }

        @keyframes welcomeFloat {
            0%, 100% { transform: translate(0, 0) scale(1); }
            50% { transform: translate(-20px, -20px) scale(1.1); }
        }

        .welcome-title {
            font-family: 'Fredoka One', cursive;
            font-size: 2rem;
            margin-bottom: 0.5rem;
            position: relative;
            z-index: 1;
        }

        .welcome-subtitle {
            font-size: 1rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .game-modes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        @media (max-width: 768px) {
            .game-modes-grid {
                grid-template-columns: 1fr 1fr;
                gap: 1rem;
            }
        }

        .mode-card {
            background: white;
            border-radius: 20px;
            padding: 1.5rem 1rem;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
            border: 3px solid transparent;
        }

        .mode-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea, #764ba2);
            z-index: -1;
            margin: -3px;
            border-radius: inherit;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .mode-card:hover {
            transform: translateY(-8px) scale(1.05);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
        }

        .mode-card:hover::before {
            opacity: 1;
        }

        .mode-card:hover .mode-title,
        .mode-card:hover .mode-desc {
            color: white;
        }

        .mode-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }

        .mode-title {
            font-family: 'Fredoka One', cursive;
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
            color: #333;
            transition: color 0.3s ease;
        }

        .mode-desc {
            font-size: 0.8rem;
            color: #666;
            line-height: 1.4;
            transition: color 0.3s ease;
        }

        .stats-section {
            background: white;
            border-radius: 20px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .stats-title {
            font-family: 'Fredoka One', cursive;
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 1rem;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr 1fr;
            }
        }

        .stat-item {
            background: linear-gradient(135deg, #ffeaa7, #fab1a0);
            border-radius: 15px;
            padding: 1rem;
            text-align: center;
            color: #333;
        }

        .stat-number {
            font-family: 'Fredoka One', cursive;
            font-size: 1.5rem;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.8rem;
            font-weight: 600;
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .action-btn {
            flex: 1;
            background: linear-gradient(135deg, #00b894, #00cec9);
            border: none;
            border-radius: 15px;
            padding: 1rem;
            color: white;
            font-family: 'Fredoka One', cursive;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
        }

        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 184, 148, 0.4);
        }

        .action-btn.secondary {
            background: linear-gradient(135deg, #fd79a8, #e84393);
            box-shadow: 0 4px 15px rgba(253, 121, 168, 0.3);
        }

        .action-btn.secondary:hover {
            box-shadow: 0 8px 25px rgba(253, 121, 168, 0.4);
        }

        .bottom-nav {
            background: white;
            border-radius: 20px 20px 0 0;
            padding: 1rem;
            display: flex;
            justify-content: space-around;
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
            margin-top: auto;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.25rem;
            padding: 0.5rem;
            border-radius: 12px;
            transition: all 0.3s ease;
            cursor: pointer;
            color: #666;
        }

        .nav-item:hover,
        .nav-item.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            transform: translateY(-2px);
        }

        .nav-icon {
            font-size: 1.5rem;
        }

        .nav-label {
            font-size: 0.7rem;
            font-weight: 600;
        }

        @keyframes heroRotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hero-title {
            font-family: 'Space Grotesk', sans-serif;
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: titleGradient 4s ease-in-out infinite;
            position: relative;
            z-index: 1;
            text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
        }

        @keyframes titleGradient {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .hero-subtitle {
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 2rem;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.6;
            position: relative;
            z-index: 1;
            font-weight: 500;
        }

        .hero-stats {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 20px;
            padding: 1.5rem 1rem;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .stat-item:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 10px 25px rgba(255, 107, 107, 0.3);
            border-color: rgba(255, 107, 107, 0.5);
        }

        .stat-number {
            font-size: 1.8rem;
            font-weight: 800;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: block;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.8);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            font-weight: 600;
        }

        .game-modes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .mode-card {
            background: rgba(255, 255, 255, 0.95);
            border: 3px solid transparent;
            border-radius: 25px;
            padding: 2rem 1.5rem;
            backdrop-filter: blur(20px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .mode-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            z-index: -1;
            margin: -3px;
            border-radius: inherit;
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        .mode-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 15px 40px rgba(255, 107, 107, 0.3);
        }

        .mode-card:hover::before {
            opacity: 1;
        }

        .mode-card:nth-child(1) { animation-delay: 0.1s; }
        .mode-card:nth-child(2) { animation-delay: 0.2s; }
        .mode-card:nth-child(3) { animation-delay: 0.3s; }
        .mode-card:nth-child(4) { animation-delay: 0.4s; }

        .mode-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .mode-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .mode-title {
            font-family: 'Space Grotesk', sans-serif;
            font-size: 1.25rem;
            font-weight: 600;
            color: white;
        }

        .mode-description {
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .mode-stats {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .mode-stat {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 8px;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            color: #3b82f6;
        }

        .mode-action {
            width: 100%;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .mode-action:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .player-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            backdrop-filter: blur(20px);
        }

        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: bold;
            color: white;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
        }

        .player-name {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .player-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 15px;
        }

        .stat-item {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #00ffff;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #cccccc;
        }

        .battle-modes {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .mode-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 30px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(20px);
            height: 180px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .mode-card:hover {
            transform: translateY(-5px);
            border-color: rgba(255, 255, 255, 0.4);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            background: rgba(255, 255, 255, 0.15);
        }

        .mode-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.9;
        }

        .mode-title {
            font-family: 'Orbitron', monospace;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: white;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .mode-description {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.4;
            font-weight: 300;
        }

        .quick-actions {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .action-btn {
            flex: 1;
            padding: 16px 24px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
            background: linear-gradient(135deg, #7c8df0, #8a5fb8);
        }

        .action-btn.secondary {
            background: linear-gradient(135deg, #764ba2, #667eea);
            box-shadow: 0 4px 15px rgba(118, 75, 162, 0.3);
        }

        .action-btn.secondary:hover {
            box-shadow: 0 6px 20px rgba(118, 75, 162, 0.4);
            background: linear-gradient(135deg, #8a5fb8, #7c8df0);
        }

        .online-players {
            max-height: 300px;
            overflow-y: auto;
        }

        .player-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin-bottom: 10px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            border-left: 3px solid #00ffff;
        }

        .player-status {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #00ff00;
            margin-right: 10px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .player-info {
            flex: 1;
        }

        .challenge-btn {
            padding: 5px 10px;
            background: rgba(255, 51, 102, 0.8);
            border: none;
            border-radius: 5px;
            color: white;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .challenge-btn:hover {
            background: rgba(255, 51, 102, 1);
            transform: scale(1.05);
        }

        .news-item {
            background: rgba(0, 0, 0, 0.2);
            border-left: 3px solid #ff3366;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 8px;
        }

        .news-title {
            font-weight: 600;
            margin-bottom: 5px;
            color: #ff3366;
        }

        .news-content {
            font-size: 0.9rem;
            color: #cccccc;
            line-height: 1.4;
        }

        .news-time {
            font-size: 0.8rem;
            color: #888;
            margin-top: 5px;
        }

        @media (max-width: 1200px) {
            .container {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto auto auto;
            }

            .sidebar {
                order: 2;
            }

            .main-content {
                order: 1;
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <!-- 左上角 - 個人資料 -->
        <div class="profile-section">
            <div class="profile-avatar">P1</div>
            <div class="profile-name">玩家001</div>
            <div class="level-info">
                <div class="level-text">
                    <span>等級 15</span>
                    <span>1,250 / 2,000 XP</span>
                </div>
                <div class="level-bar">
                    <div class="level-progress"></div>
                </div>
            </div>
        </div>

        <!-- 右上角 - 設定區域 -->
        <div class="settings-section">
            <h3 class="settings-title">設定</h3>
            <button class="settings-btn primary" onclick="openSettings()">
                ⚙️ 遊戲設定
            </button>
            <button class="settings-btn danger" onclick="logout()">
                🚪 登出
            </button>
        </div>

        <!-- 左下角 - 其他功能 -->
        <div class="other-functions-section">
            <h3 class="functions-title">其他功能</h3>
            <div class="functions-grid">
                <button class="function-btn" onclick="openLeaderboard()">
                    <div class="function-icon">🏆</div>
                    <div>排行榜</div>
                </button>
                <button class="function-btn" onclick="openFriends()">
                    <div class="function-icon">👥</div>
                    <div>好友</div>
                </button>
                <button class="function-btn" onclick="openShop()">
                    <div class="function-icon">🛒</div>
                    <div>商店</div>
                </button>
                <button class="function-btn" onclick="openAchievements()">
                    <div class="function-icon">🎖️</div>
                    <div>成就</div>
                </button>
            </div>
        </div>

        <!-- 右下角 - 對戰區域 -->
        <div class="battle-section">
            <h2 class="battle-title">開始對戰</h2>
            <p class="battle-subtitle">準備好迎接終極方塊競技場挑戰了嗎？</p>
            <button class="battle-btn" onclick="startGame()">
                ⚡ 立即開戰
            </button>
        </div>
    </div>

    <script>
        // 遊戲功能
        function startQuickMatch() {
            console.log('開始快速對戰');
            window.location.href = 'index.html';
        }

        function startRankedMatch() {
            console.log('開始排位賽');
            alert('排位賽功能即將推出！');
        }

        function createCustomRoom() {
            console.log('創建自定義房間');
            alert('自定義房間功能即將推出！');
        }

        function openSkillSelection() {
            console.log('打開技能選擇');
            window.location.href = 'index.html';
        }

        function startGame() {
            console.log('開始遊戲');
            window.location.href = 'index.html';
        }

        function openSettings() {
            console.log('打開設置');
            alert('設置功能即將推出！');
        }

        function challengePlayer(playerName) {
            console.log(`挑戰玩家: ${playerName}`);
            if (confirm(`確定要挑戰 ${playerName} 嗎？`)) {
                alert(`正在向 ${playerName} 發送挑戰邀請...`);
                // 這裡可以添加實際的挑戰邏輯
            }
        }

        function logout() {
            console.log('登出');
            if (confirm('確定要登出嗎？')) {
                alert('已登出！');
            }
        }

        function openLeaderboard() {
            console.log('打開排行榜');
            alert('排行榜功能即將推出！');
        }

        function openFriends() {
            console.log('打開好友');
            alert('好友功能即將推出！');
        }

        function openShop() {
            console.log('打開商店');
            alert('商店功能即將推出！');
        }

        function openAchievements() {
            console.log('打開成就');
            alert('成就功能即將推出！');
        }

        // 頁面載入完成後的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Block Battle Arena 主大廳已載入');

            // 四角區域載入動畫
            const sections = document.querySelectorAll('.profile-section, .settings-section, .other-functions-section, .battle-section');
            sections.forEach((section, index) => {
                section.style.opacity = '0';
                section.style.transform = 'scale(0.9)';
                setTimeout(() => {
                    section.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                    section.style.opacity = '1';
                    section.style.transform = 'scale(1)';
                }, index * 150);
            });
        });

        // CSS 動畫
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
