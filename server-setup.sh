#!/bin/bash

# Block Battle Arena 服務器設置腳本
# 用於在 Ubuntu 服務器上設置完整的運行環境

echo "=========================================="
echo "Block Battle Arena - 服務器設置腳本"
echo "=========================================="
echo ""

# 更新系統
echo "🔄 更新系統套件..."
sudo apt update && sudo apt upgrade -y

# 安裝 Node.js
echo "📦 安裝 Node.js..."
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 驗證安裝
echo "✅ Node.js 版本:"
node --version
npm --version

# 安裝 PM2
echo "🔧 安裝 PM2..."
sudo npm install -g pm2

# 安裝 Nginx
echo "🌐 安裝 Nginx..."
sudo apt install -y nginx

# 啟動並啟用 Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# 安裝 Certbot (Let's Encrypt)
echo "🔒 安裝 Certbot..."
sudo apt install -y certbot python3-certbot-nginx

# 創建項目目錄
echo "📁 創建項目目錄..."
sudo mkdir -p /var/www/block-battle-arena
sudo mkdir -p /var/www/block-battle-arena/logs
sudo chown -R $USER:$USER /var/www/block-battle-arena

# 配置防火牆
echo "🔥 配置防火牆..."
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 3000/tcp
sudo ufw --force enable

# 創建 Nginx 配置
echo "⚙️ 配置 Nginx..."
sudo cp nginx.conf /etc/nginx/sites-available/p1p2blockgame.com
sudo ln -sf /etc/nginx/sites-available/p1p2blockgame.com /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# 測試 Nginx 配置
sudo nginx -t

# 重啟 Nginx
sudo systemctl restart nginx

echo ""
echo "✅ 服務器基礎設置完成！"
echo ""
echo "🔒 接下來需要設置 SSL 證書:"
echo "   sudo certbot --nginx -d p1p2blockgame.com -d www.p1p2blockgame.com"
echo ""
echo "🚀 然後可以部署應用:"
echo "   ./deploy.sh"
echo ""
echo "📊 有用的命令:"
echo "   查看 Nginx 狀態: sudo systemctl status nginx"
echo "   查看 Nginx 日誌: sudo tail -f /var/log/nginx/error.log"
echo "   重啟 Nginx: sudo systemctl restart nginx"
echo "   查看防火牆狀態: sudo ufw status"
echo ""
