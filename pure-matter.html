<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pure Matter.js Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a2e;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        #canvas-container {
            border: 2px solid #00d4ff;
            border-radius: 10px;
            background: #000;
            width: 800px;
            height: 600px;
            margin: 20px auto;
            position: relative;
        }
        
        canvas {
            border-radius: 8px;
        }
        
        .info {
            text-align: center;
            margin: 20px 0;
        }
        
        .debug {
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="info">
        <h1>純 Matter.js 測試</h1>
        <p>直接使用 Matter.js 引擎，不通過 Phaser</p>
    </div>
    
    <div id="canvas-container"></div>
    
    <div class="info">
        <button onclick="addBall()">添加球</button>
        <button onclick="resetTest()">重置</button>
        <button onclick="toggleWireframes()">切換線框</button>
    </div>
    
    <div class="debug" id="debug-info">
        載入中...
    </div>

    <script src="https://cdn.jsdelivr.net/npm/matter-js@0.19.0/build/matter.min.js"></script>
    
    <script>
        let engine, render, world;
        let balls = [];
        let walls = [];
        let debugInfo = document.getElementById('debug-info');
        
        function log(message) {
            console.log(message);
            debugInfo.innerHTML += message + '<br>';
            debugInfo.scrollTop = debugInfo.scrollHeight;
        }
        
        function init() {
            log('初始化純 Matter.js 引擎...');
            
            // 創建引擎
            engine = Matter.Engine.create();
            world = engine.world;
            
            // 禁用重力
            engine.world.gravity.y = 0;
            engine.world.gravity.x = 0;
            log('重力已禁用');
            
            // 創建渲染器
            render = Matter.Render.create({
                element: document.getElementById('canvas-container'),
                engine: engine,
                options: {
                    width: 800,
                    height: 600,
                    wireframes: false,
                    background: '#1a1a2e',
                    showAngleIndicator: true,
                    showVelocity: true
                }
            });
            
            // 創建邊界
            createWalls();
            
            // 創建測試球
            createTestBall();
            
            // 啟動引擎和渲染器
            Matter.Engine.run(engine);
            Matter.Render.run(render);
            
            // 設置碰撞監聽
            Matter.Events.on(engine, 'collisionStart', function(event) {
                log(`碰撞檢測: ${event.pairs.length} 對碰撞`);
            });
            
            // 開始更新循環
            startUpdateLoop();
            
            log('Matter.js 引擎初始化完成');
        }
        
        function createWalls() {
            log('創建邊界牆壁...');
            
            const wallThickness = 50;
            const wallOptions = {
                isStatic: true,
                restitution: 1.0,
                friction: 0,
                frictionStatic: 0,
                frictionAir: 0,
                render: {
                    fillStyle: '#666666'
                }
            };
            
            // 創建四面牆
            const topWall = Matter.Bodies.rectangle(400, wallThickness/2, 800, wallThickness, wallOptions);
            const bottomWall = Matter.Bodies.rectangle(400, 600 - wallThickness/2, 800, wallThickness, wallOptions);
            const leftWall = Matter.Bodies.rectangle(wallThickness/2, 300, wallThickness, 600, wallOptions);
            const rightWall = Matter.Bodies.rectangle(800 - wallThickness/2, 300, wallThickness, 600, wallOptions);
            
            walls = [topWall, bottomWall, leftWall, rightWall];
            
            // 添加到世界
            Matter.World.add(world, walls);
            
            log('4面牆壁創建完成');
        }
        
        function createTestBall() {
            log('創建測試球...');
            
            const ball = Matter.Bodies.circle(400, 300, 20, {
                restitution: 1.0,     // 完美彈性
                friction: 0,          // 無摩擦
                frictionAir: 0,       // 無空氣阻力
                density: 1,           // 標準密度
                inertia: Infinity,    // 防止旋轉
                render: {
                    fillStyle: '#00d4ff',
                    strokeStyle: '#ffffff',
                    lineWidth: 3
                }
            });
            
            // 設置初始速度
            Matter.Body.setVelocity(ball, { x: 5, y: 3 });
            
            // 添加到世界
            Matter.World.add(world, ball);
            balls.push(ball);
            
            log('測試球創建完成，初始速度: (5, 3)');
        }
        
        function addBall() {
            const x = 200 + Math.random() * 400;
            const y = 150 + Math.random() * 300;
            const radius = 15 + Math.random() * 10;
            const color = `hsl(${Math.random() * 360}, 70%, 60%)`;
            
            const ball = Matter.Bodies.circle(x, y, radius, {
                restitution: 1.0,
                friction: 0,
                frictionAir: 0,
                density: 1,
                inertia: Infinity,
                render: {
                    fillStyle: color,
                    strokeStyle: '#ffffff',
                    lineWidth: 2
                }
            });
            
            // 隨機初始速度
            const vx = (Math.random() - 0.5) * 10;
            const vy = (Math.random() - 0.5) * 10;
            Matter.Body.setVelocity(ball, { x: vx, y: vy });
            
            Matter.World.add(world, ball);
            balls.push(ball);
            
            log(`新球添加: 位置(${x.toFixed(1)}, ${y.toFixed(1)}) 速度(${vx.toFixed(1)}, ${vy.toFixed(1)})`);
        }
        
        function resetTest() {
            log('重置測試...');
            
            // 移除所有球
            Matter.World.remove(world, balls);
            balls = [];
            
            // 重新創建測試球
            createTestBall();
            
            log('測試重置完成');
        }
        
        function toggleWireframes() {
            render.options.wireframes = !render.options.wireframes;
            log(`線框模式: ${render.options.wireframes ? '開啟' : '關閉'}`);
        }
        
        function startUpdateLoop() {
            let frameCount = 0;
            
            function update() {
                frameCount++;
                
                // 每60幀檢查一次球的狀態
                if (frameCount % 60 === 0) {
                    balls.forEach((ball, index) => {
                        const speed = Math.sqrt(ball.velocity.x ** 2 + ball.velocity.y ** 2);
                        
                        // 如果速度太低，重新設置
                        if (speed < 0.5) {
                            const angle = Math.random() * Math.PI * 2;
                            const newSpeed = 3 + Math.random() * 3;
                            Matter.Body.setVelocity(ball, {
                                x: Math.cos(angle) * newSpeed,
                                y: Math.sin(angle) * newSpeed
                            });
                            log(`球 ${index} 速度重置: 新速度 ${newSpeed.toFixed(1)}`);
                        }
                        
                        // 記錄第一個球的狀態
                        if (index === 0) {
                            log(`球 0 狀態: 位置(${ball.position.x.toFixed(1)}, ${ball.position.y.toFixed(1)}) 速度(${ball.velocity.x.toFixed(1)}, ${ball.velocity.y.toFixed(1)}) 速率: ${speed.toFixed(1)}`);
                        }
                    });
                }
                
                requestAnimationFrame(update);
            }
            
            update();
        }
        
        // 全域函數
        window.addBall = addBall;
        window.resetTest = resetTest;
        window.toggleWireframes = toggleWireframes;
        
        // 錯誤處理
        window.addEventListener('error', (event) => {
            log(`錯誤: ${event.error ? event.error.message : 'Unknown error'}`);
        });
        
        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('開始初始化純 Matter.js 測試...');
            
            try {
                init();
                log('純 Matter.js 測試初始化成功');
            } catch (error) {
                log(`初始化失敗: ${error.message}`);
            }
        });
    </script>
</body>
</html>
