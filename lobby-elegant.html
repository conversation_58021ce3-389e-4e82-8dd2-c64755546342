<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Block Battle Arena - 主大廳</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background:
                linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%),
                radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 頭部 */
        .header {
            text-align: center;
            margin-bottom: 4rem;
            padding: 3rem 0;
        }

        .logo {
            font-size: 3.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 1rem;
            letter-spacing: -2px;
        }

        .subtitle {
            font-size: 1.1rem;
            color: #888;
            font-weight: 400;
        }

        /* 主要內容 */
        .main-content {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 4rem;
        }

        /* 側邊欄 */
        .sidebar {
            background: #1a1a1a;
            border-radius: 16px;
            padding: 2rem;
            height: fit-content;
            border: 1px solid #333;
        }

        .player-section {
            margin-bottom: 2rem;
        }

        .player-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            margin: 0 auto 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: 600;
            color: white;
        }

        .player-name {
            text-align: center;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .stat-item {
            background: #222;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            border: 1px solid #333;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #888;
        }

        /* 遊戲區域 */
        .game-area {
            background: #1a1a1a;
            border-radius: 16px;
            padding: 3rem;
            border: 1px solid #333;
        }

        .section-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 2rem;
            color: white;
        }

        .game-modes {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .mode-card {
            background: #222;
            border: 1px solid #333;
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .mode-card:hover {
            background: #2a2a2a;
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .mode-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }

        .mode-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: white;
        }

        .mode-desc {
            font-size: 0.9rem;
            color: #888;
            line-height: 1.4;
        }

        /* 快速操作 */
        .quick-actions {
            display: flex;
            gap: 1rem;
        }

        .action-btn {
            flex: 1;
            background: #667eea;
            border: none;
            border-radius: 8px;
            padding: 1rem 2rem;
            color: white;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }

        .action-btn.secondary {
            background: #333;
            color: #ccc;
        }

        .action-btn.secondary:hover {
            background: #444;
        }

        /* 響應式 */
        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
        }

        @media (max-width: 768px) {
            .game-modes {
                grid-template-columns: 1fr;
            }

            .quick-actions {
                flex-direction: column;
            }

            .logo {
                font-size: 2.5rem;
            }
        }

        /* 載入動畫 */
        .fade-in {
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.6s ease forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 頭部 -->
        <header class="header fade-in">
            <h1 class="logo">Block Battle Arena</h1>
            <p class="subtitle">競技場等待著你的挑戰</p>
        </header>

        <!-- 主要內容 -->
        <div class="main-content">
            <!-- 側邊欄 -->
            <aside class="sidebar fade-in">
                <div class="player-section">
                    <div class="player-avatar">P1</div>
                    <div class="player-name">玩家001</div>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number">42</div>
                            <div class="stat-label">勝利</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">18</div>
                            <div class="stat-label">失敗</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">70%</div>
                            <div class="stat-label">勝率</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">156</div>
                            <div class="stat-label">排名</div>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 遊戲區域 -->
            <main class="game-area fade-in">
                <h2 class="section-title">選擇遊戲模式</h2>

                <div class="game-modes">
                    <div class="mode-card" onclick="startGame()">
                        <div class="mode-icon">⚡</div>
                        <div class="mode-title">快速對戰</div>
                        <div class="mode-desc">立即匹配對手，開始激烈的1v1戰鬥</div>
                    </div>

                    <div class="mode-card" onclick="openRanked()">
                        <div class="mode-icon">🏆</div>
                        <div class="mode-title">排位賽</div>
                        <div class="mode-desc">參與排位賽，提升你的競技場排名</div>
                    </div>

                    <div class="mode-card" onclick="createRoom()">
                        <div class="mode-icon">🎮</div>
                        <div class="mode-title">自定義房間</div>
                        <div class="mode-desc">創建房間，邀請朋友一起對戰</div>
                    </div>

                    <div class="mode-card" onclick="openTraining()">
                        <div class="mode-icon">🎯</div>
                        <div class="mode-title">技能訓練</div>
                        <div class="mode-desc">練習技能組合，完善你的戰術</div>
                    </div>
                </div>

                <div class="quick-actions">
                    <button class="action-btn" onclick="startGame()">立即開始</button>
                    <button class="action-btn secondary" onclick="openSettings()">設置</button>
                </div>
            </main>
        </div>
    </div>

    <script>
        function startGame() {
            window.location.href = 'index.html';
        }

        function openRanked() {
            alert('排位賽功能即將推出！');
        }

        function createRoom() {
            alert('自定義房間功能即將推出！');
        }

        function openTraining() {
            window.location.href = 'index.html';
        }

        function openSettings() {
            alert('設置功能即將推出！');
        }

        // 載入動畫
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.fade-in');
            elements.forEach((el, index) => {
                el.style.animationDelay = `${index * 0.1}s`;
            });
        });
    </script>
</body>
</html>
