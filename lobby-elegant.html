<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Block Battle Arena - 主大廳</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background:
                linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%),
                radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            width: 100vw;
            height: 100vh;
            padding: 2rem;
            display: grid;
            grid-template-areas:
                "player-info currency title"
                ". . ."
                "mode1 mode2 mode3"
                "mode4 actions .";
            grid-template-columns: 300px 300px 1fr;
            grid-template-rows: auto 1fr auto auto;
            gap: 2rem;
            position: relative;
        }

        /* 玩家信息區域 - 左上角 */
        .player-info {
            grid-area: player-info;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            height: fit-content;
        }

        /* 標題區域 - 中央上方 */
        .title-section {
            grid-area: title;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .logo {
            font-size: 3.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 1rem;
            letter-spacing: -2px;
        }

        .subtitle {
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 400;
        }

        /* 貨幣區域 - 右上角 */
        .currency-section {
            grid-area: currency;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            height: fit-content;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .currency-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .currency-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .currency-icon.gold {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }

        .currency-icon.gem {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        }

        .currency-info {
            flex: 1;
        }

        .currency-label {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 0.25rem;
        }

        .currency-amount {
            font-size: 1.3rem;
            font-weight: 700;
            color: white;
        }

        /* 遊戲模式卡片 */
        .mode1 { grid-area: mode1; }
        .mode2 { grid-area: mode2; }
        .mode3 { grid-area: mode3; }
        .mode4 { grid-area: mode4; }

        .player-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            margin: 0 auto 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            font-weight: 700;
            color: white;
            box-shadow:
                0 8px 25px rgba(59, 130, 246, 0.3),
                0 0 0 4px rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .player-avatar::after {
            content: '';
            position: absolute;
            top: -2px;
            right: -2px;
            width: 24px;
            height: 24px;
            background: linear-gradient(135deg, #10b981, #059669);
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.9);
        }

        .player-name {
            text-align: center;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 1rem;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 1.25rem;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .stat-item:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
        }

        .stat-number {
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.85rem;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 500;
        }

        /* 操作按鈕區域 */
        .actions-section {
            grid-area: actions;
            display: flex;
            gap: 2rem;
            justify-content: center;
            align-items: center;
        }

        .mode-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 2.5rem 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .mode-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .mode-card:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(59, 130, 246, 0.4);
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(59, 130, 246, 0.2);
        }

        .mode-card:hover::before {
            opacity: 1;
        }

        .mode-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }

        .mode-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: white;
        }

        .mode-desc {
            font-size: 0.9rem;
            color: #888;
            line-height: 1.4;
        }



        .action-btn {
            flex: 1;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            border: none;
            border-radius: 12px;
            padding: 1.25rem 2rem;
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
            position: relative;
            overflow: hidden;
        }

        .action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }

        .action-btn:hover::before {
            left: 100%;
        }

        .action-btn.secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .action-btn.secondary:hover {
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        /* 響應式 */
        @media (max-width: 1200px) {
            .container {
                grid-template-areas:
                    "player-info currency"
                    "title title"
                    "mode1 mode2"
                    "mode3 mode4"
                    "actions actions";
                grid-template-columns: 1fr 1fr;
                grid-template-rows: auto auto auto auto auto;
            }
        }

        @media (max-width: 768px) {
            .container {
                grid-template-areas:
                    "player-info"
                    "currency"
                    "title"
                    "mode1"
                    "mode2"
                    "mode3"
                    "mode4"
                    "actions";
                grid-template-columns: 1fr;
                grid-template-rows: auto auto auto auto auto auto auto auto;
                padding: 1rem;
            }

            .player-info, .currency-section {
                padding: 1.5rem;
            }

            .stats-grid {
                grid-template-columns: 1fr 1fr;
            }

            .logo {
                font-size: 2.5rem;
            }

            .actions-section {
                flex-direction: column;
                gap: 1rem;
            }
        }

        /* 載入動畫 */
        .fade-in {
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.6s ease forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 玩家信息 - 左上角 -->
        <div class="player-info fade-in">
            <div class="player-avatar">P1</div>
            <div class="player-name">玩家001</div>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">42</div>
                    <div class="stat-label">勝利</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">18</div>
                    <div class="stat-label">失敗</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">156</div>
                    <div class="stat-label">排名</div>
                </div>
            </div>
        </div>

        <!-- 貨幣信息 - 右上角 -->
        <div class="currency-section fade-in">
            <div class="currency-item">
                <div class="currency-icon gold">💰</div>
                <div class="currency-info">
                    <div class="currency-label">金幣</div>
                    <div class="currency-amount">1,250</div>
                </div>
            </div>
            <div class="currency-item">
                <div class="currency-icon gem">💎</div>
                <div class="currency-info">
                    <div class="currency-label">寶石</div>
                    <div class="currency-amount">45</div>
                </div>
            </div>
        </div>

        <!-- 標題 - 中央上方 -->
        <div class="title-section fade-in">
            <h1 class="logo">Block Battle Arena</h1>
            <p class="subtitle">競技場等待著你的挑戰</p>
        </div>

        <!-- 遊戲模式卡片 -->
        <div class="mode-card mode1 fade-in" onclick="startGame()">
            <div class="mode-icon">⚡</div>
            <div class="mode-title">快速對戰</div>
            <div class="mode-desc">立即匹配對手，開始激烈的1v1戰鬥</div>
        </div>

        <div class="mode-card mode2 fade-in" onclick="openRanked()">
            <div class="mode-icon">🏆</div>
            <div class="mode-title">排位賽</div>
            <div class="mode-desc">參與排位賽，提升你的競技場排名</div>
        </div>

        <div class="mode-card mode3 fade-in" onclick="createRoom()">
            <div class="mode-icon">🎮</div>
            <div class="mode-title">自定義房間</div>
            <div class="mode-desc">創建房間，邀請朋友一起對戰</div>
        </div>

        <div class="mode-card mode4 fade-in" onclick="openSettings()">
            <div class="mode-icon">⚙️</div>
            <div class="mode-title">設置</div>
            <div class="mode-desc">調整遊戲設定和個人偏好</div>
        </div>

        <!-- 操作按鈕 - 底部中央 -->
        <div class="actions-section fade-in">
            <button class="action-btn" onclick="startGame()">立即開始遊戲</button>
        </div>
    </div>

    <script>
        function startGame() {
            window.location.href = 'index.html';
        }

        function openRanked() {
            alert('排位賽功能即將推出！');
        }

        function createRoom() {
            alert('自定義房間功能即將推出！');
        }

        function openTraining() {
            window.location.href = 'index.html';
        }

        function openSettings() {
            alert('設置功能即將推出！');
        }

        // 載入動畫
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.fade-in');
            elements.forEach((el, index) => {
                el.style.animationDelay = `${index * 0.1}s`;
            });
        });
    </script>
</body>
</html>
