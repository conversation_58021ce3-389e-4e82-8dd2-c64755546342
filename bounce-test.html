<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bounce Test - 反彈測試</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a2e;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        #game-container {
            border: 2px solid #00d4ff;
            border-radius: 10px;
            background: #000;
            width: 800px;
            height: 600px;
            margin: 20px auto;
        }
        
        .info {
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="info">
        <h1>反彈測試</h1>
        <p>測試方塊是否能在邊界正確反彈</p>
    </div>
    
    <div id="game-container"></div>
    
    <div class="info">
        <p>如果方塊能正確反彈，表示物理引擎設置正確</p>
        <button onclick="resetBalls()">重置方塊</button>
        <button onclick="toggleDebug()">切換調試</button>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/matter-js@0.19.0/build/matter.min.js"></script>
    
    <script>
        let game;
        let scene;
        
        class BounceTestScene extends Phaser.Scene {
            constructor() {
                super({ key: 'BounceTestScene' });
                this.balls = [];
            }

            create() {
                console.log('BounceTestScene: Creating...');
                
                // 設置物理世界 - 使用最簡單的方法
                this.matter.world.disableGravity();
                
                // 使用 Phaser 的內建邊界系統
                this.matter.world.setBounds(0, 0, 800, 600, 32, true, true, false, true);
                
                // 創建測試方塊
                this.createTestBalls();
                
                // 添加說明文字
                this.add.text(400, 50, '反彈測試', {
                    fontSize: '24px',
                    fill: '#ffffff',
                    fontFamily: 'Arial'
                }).setOrigin(0.5);
                
                this.add.text(400, 80, '方塊應該在邊界反彈', {
                    fontSize: '16px',
                    fill: '#cccccc',
                    fontFamily: 'Arial'
                }).setOrigin(0.5);
                
                // 速度顯示
                this.speedText = this.add.text(20, 20, 'Speeds:', {
                    fontSize: '14px',
                    fill: '#ffffff',
                    fontFamily: 'Arial'
                });
                
                console.log('BounceTestScene: Created successfully');
            }

            createTestBalls() {
                // 創建幾個測試方塊
                this.addBall(200, 200, 0x00d4ff, 4, 3);
                this.addBall(600, 400, 0xff6b35, -3, -4);
                this.addBall(400, 300, 0x00ff00, 5, -2);
            }

            addBall(x, y, color, vx, vy) {
                // 創建物理實體 - 使用最簡單的設置
                const ballBody = this.matter.add.rectangle(x, y, 30, 30, {
                    restitution: 1.0,    // 完美彈性
                    friction: 0,         // 無摩擦
                    frictionAir: 0,      // 無空氣阻力
                    density: 1,          // 標準密度
                    inertia: Infinity    // 防止旋轉
                });
                
                // 創建視覺實體
                const ballSprite = this.add.rectangle(x, y, 30, 30, color);
                ballSprite.setStrokeStyle(2, 0xffffff);
                
                // 設置初始速度
                this.matter.body.setVelocity(ballBody, { x: vx, y: vy });
                
                // 保存引用
                const ball = { 
                    body: ballBody, 
                    sprite: ballSprite, 
                    id: this.balls.length,
                    color: color
                };
                this.balls.push(ball);
                
                console.log(`Ball ${ball.id} created at (${x}, ${y}) with velocity (${vx}, ${vy})`);
                
                return ball;
            }

            update() {
                // 更新所有球的視覺位置
                this.balls.forEach(ball => {
                    if (ball.body && ball.sprite) {
                        ball.sprite.setPosition(ball.body.position.x, ball.body.position.y);
                        
                        // 檢查速度是否保持
                        const speed = Math.sqrt(ball.body.velocity.x ** 2 + ball.body.velocity.y ** 2);
                        if (speed < 1) {
                            console.warn(`Ball ${ball.id} speed too low: ${speed}`);
                            // 重新設置速度
                            const angle = Math.random() * Math.PI * 2;
                            this.matter.body.setVelocity(ball.body, {
                                x: Math.cos(angle) * 5,
                                y: Math.sin(angle) * 5
                            });
                        }
                    }
                });
                
                // 更新速度顯示
                if (this.speedText && this.balls.length > 0) {
                    let speedInfo = 'Speeds:\n';
                    this.balls.forEach((ball, index) => {
                        if (ball.body) {
                            const vx = ball.body.velocity.x.toFixed(1);
                            const vy = ball.body.velocity.y.toFixed(1);
                            const speed = Math.sqrt(ball.body.velocity.x ** 2 + ball.body.velocity.y ** 2).toFixed(1);
                            speedInfo += `Ball ${index}: (${vx}, ${vy}) |${speed}|\n`;
                        }
                    });
                    this.speedText.setText(speedInfo);
                }
            }

            resetBalls() {
                // 清除現有的球
                this.balls.forEach(ball => {
                    if (ball.body) {
                        this.matter.world.remove(ball.body);
                    }
                    if (ball.sprite) {
                        ball.sprite.destroy();
                    }
                });
                this.balls = [];
                
                // 重新創建
                this.createTestBalls();
                console.log('Balls reset');
            }
        }

        // 遊戲配置
        const gameConfig = {
            type: Phaser.AUTO,
            width: 800,
            height: 600,
            parent: 'game-container',
            backgroundColor: '#1a1a2e',
            scene: [BounceTestScene],
            physics: {
                default: 'matter',
                matter: {
                    gravity: { x: 0, y: 0 },
                    debug: false,
                    debugBodyColor: 0x00ff00,
                    debugBodyFillColor: 0x00ff00,
                    debugStaticBodyColor: 0xff0000,
                    debugVelocityColor: 0x00ffff
                }
            }
        };

        // 全域函數
        window.resetBalls = function() {
            if (scene) {
                scene.resetBalls();
            }
        };

        window.toggleDebug = function() {
            if (game && game.scene.scenes[0]) {
                const currentScene = game.scene.scenes[0];
                if (currentScene.matter) {
                    const debug = currentScene.matter.world.debugGraphic;
                    if (debug) {
                        debug.visible = !debug.visible;
                    } else {
                        currentScene.matter.world.createDebugGraphic();
                    }
                }
            }
        };

        // 錯誤處理
        window.addEventListener('error', (event) => {
            console.error('Error:', event.error);
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Initializing bounce test...');
            
            try {
                game = new Phaser.Game(gameConfig);
                scene = game.scene.scenes[0];
                console.log('Bounce test initialized successfully');
            } catch (error) {
                console.error('Initialization failed:', error);
            }
        });
    </script>
</body>
</html>
