<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Perfect Bounce Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a2e;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        #game-container {
            border: 2px solid #00d4ff;
            border-radius: 10px;
            background: #000;
            width: 800px;
            height: 600px;
            margin: 20px auto;
        }
        
        .info {
            text-align: center;
            margin: 20px 0;
        }
        
        .debug {
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 150px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="info">
        <h1>完美反彈測試</h1>
        <p>手動實現反彈邏輯，確保完美反彈</p>
    </div>
    
    <div id="game-container"></div>
    
    <div class="info">
        <button onclick="addBall()">添加球</button>
        <button onclick="resetTest()">重置</button>
        <button onclick="togglePhysics()">切換物理/手動</button>
    </div>
    
    <div class="debug" id="debug-info">
        載入中...
    </div>

    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    
    <script>
        let game;
        let scene;
        let debugInfo = document.getElementById('debug-info');
        let usePhysics = false; // 默認使用手動反彈
        
        function log(message) {
            console.log(message);
            debugInfo.innerHTML += message + '<br>';
            debugInfo.scrollTop = debugInfo.scrollHeight;
        }
        
        class PerfectBounceScene extends Phaser.Scene {
            constructor() {
                super({ key: 'PerfectBounceScene' });
                this.balls = [];
                this.bounds = {
                    left: 50,
                    right: 750,
                    top: 50,
                    bottom: 550
                };
            }

            create() {
                log('PerfectBounceScene: 開始創建...');
                
                // 創建邊界視覺
                this.createBounds();
                
                // 創建測試球
                this.createTestBall();
                
                // 添加文字
                this.add.text(400, 25, '完美反彈測試', {
                    fontSize: '24px',
                    fill: '#ffffff',
                    fontFamily: 'Arial'
                }).setOrigin(0.5);
                
                this.modeText = this.add.text(20, 20, `模式: ${usePhysics ? '物理引擎' : '手動反彈'}`, {
                    fontSize: '16px',
                    fill: '#ffffff',
                    fontFamily: 'Arial'
                });
                
                this.statusText = this.add.text(20, 570, '狀態: 初始化', {
                    fontSize: '14px',
                    fill: '#ffffff',
                    fontFamily: 'Arial'
                });
                
                log('場景創建完成');
            }

            createBounds() {
                // 創建邊界視覺
                const graphics = this.add.graphics();
                graphics.lineStyle(4, 0x00d4ff, 1);
                graphics.strokeRect(
                    this.bounds.left, 
                    this.bounds.top, 
                    this.bounds.right - this.bounds.left, 
                    this.bounds.bottom - this.bounds.top
                );
                
                log(`邊界創建: (${this.bounds.left}, ${this.bounds.top}) 到 (${this.bounds.right}, ${this.bounds.bottom})`);
            }

            createTestBall() {
                const x = 400;
                const y = 300;
                const radius = 20;
                
                const ball = {
                    sprite: this.add.circle(x, y, radius, 0x00d4ff),
                    x: x,
                    y: y,
                    vx: 4,
                    vy: 3,
                    radius: radius,
                    id: this.balls.length
                };
                
                ball.sprite.setStrokeStyle(3, 0xffffff);
                this.balls.push(ball);
                
                log(`測試球創建: 位置(${x}, ${y}) 速度(${ball.vx}, ${ball.vy})`);
            }

            addBall() {
                const x = this.bounds.left + 50 + Math.random() * (this.bounds.right - this.bounds.left - 100);
                const y = this.bounds.top + 50 + Math.random() * (this.bounds.bottom - this.bounds.top - 100);
                const radius = 15 + Math.random() * 10;
                const color = Math.random() * 0xffffff;
                
                const ball = {
                    sprite: this.add.circle(x, y, radius, color),
                    x: x,
                    y: y,
                    vx: (Math.random() - 0.5) * 8,
                    vy: (Math.random() - 0.5) * 8,
                    radius: radius,
                    id: this.balls.length
                };
                
                ball.sprite.setStrokeStyle(2, 0xffffff);
                this.balls.push(ball);
                
                log(`新球添加: ID${ball.id} 位置(${x.toFixed(1)}, ${y.toFixed(1)}) 速度(${ball.vx.toFixed(1)}, ${ball.vy.toFixed(1)})`);
            }

            update(time, delta) {
                const deltaTime = delta / 1000; // 轉換為秒
                
                this.balls.forEach((ball, index) => {
                    // 更新位置
                    ball.x += ball.vx * deltaTime * 60; // 60fps 基準
                    ball.y += ball.vy * deltaTime * 60;
                    
                    // 檢查邊界碰撞並反彈
                    let bounced = false;
                    
                    // 左右邊界
                    if (ball.x - ball.radius <= this.bounds.left) {
                        ball.x = this.bounds.left + ball.radius;
                        ball.vx = Math.abs(ball.vx); // 確保向右
                        bounced = true;
                        log(`球 ${index} 左邊界反彈`);
                    } else if (ball.x + ball.radius >= this.bounds.right) {
                        ball.x = this.bounds.right - ball.radius;
                        ball.vx = -Math.abs(ball.vx); // 確保向左
                        bounced = true;
                        log(`球 ${index} 右邊界反彈`);
                    }
                    
                    // 上下邊界
                    if (ball.y - ball.radius <= this.bounds.top) {
                        ball.y = this.bounds.top + ball.radius;
                        ball.vy = Math.abs(ball.vy); // 確保向下
                        bounced = true;
                        log(`球 ${index} 上邊界反彈`);
                    } else if (ball.y + ball.radius >= this.bounds.bottom) {
                        ball.y = this.bounds.bottom - ball.radius;
                        ball.vy = -Math.abs(ball.vy); // 確保向上
                        bounced = true;
                        log(`球 ${index} 下邊界反彈`);
                    }
                    
                    // 更新視覺位置
                    ball.sprite.setPosition(ball.x, ball.y);
                    
                    // 檢查速度是否保持
                    const speed = Math.sqrt(ball.vx ** 2 + ball.vy ** 2);
                    if (speed < 2) {
                        // 重新設置速度
                        const angle = Math.random() * Math.PI * 2;
                        const newSpeed = 4;
                        ball.vx = Math.cos(angle) * newSpeed;
                        ball.vy = Math.sin(angle) * newSpeed;
                        log(`球 ${index} 速度重置: 新速度 ${newSpeed}`);
                    }
                });
                
                // 更新狀態顯示
                if (this.balls.length > 0) {
                    const ball = this.balls[0];
                    const speed = Math.sqrt(ball.vx ** 2 + ball.vy ** 2);
                    this.statusText.setText(
                        `球0: 位置(${ball.x.toFixed(1)}, ${ball.y.toFixed(1)}) ` +
                        `速度(${ball.vx.toFixed(1)}, ${ball.vy.toFixed(1)}) ` +
                        `速率: ${speed.toFixed(1)}`
                    );
                }
            }

            resetTest() {
                log('重置測試...');
                
                // 清除所有球
                this.balls.forEach(ball => {
                    if (ball.sprite) {
                        ball.sprite.destroy();
                    }
                });
                this.balls = [];
                
                // 重新創建測試球
                this.createTestBall();
                
                log('測試重置完成');
            }

            togglePhysics() {
                usePhysics = !usePhysics;
                this.modeText.setText(`模式: ${usePhysics ? '物理引擎' : '手動反彈'}`);
                log(`切換到: ${usePhysics ? '物理引擎' : '手動反彈'} 模式`);
                
                if (usePhysics) {
                    log('注意: 物理引擎模式尚未實現，仍使用手動反彈');
                }
            }
        }

        // 遊戲配置
        const gameConfig = {
            type: Phaser.AUTO,
            width: 800,
            height: 600,
            parent: 'game-container',
            backgroundColor: '#1a1a2e',
            scene: [PerfectBounceScene]
        };

        // 全域函數
        window.addBall = function() {
            if (scene) {
                scene.addBall();
            }
        };

        window.resetTest = function() {
            if (scene) {
                scene.resetTest();
            }
        };

        window.togglePhysics = function() {
            if (scene) {
                scene.togglePhysics();
            }
        };

        // 錯誤處理
        window.addEventListener('error', (event) => {
            log(`錯誤: ${event.error ? event.error.message : 'Unknown error'}`);
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('開始初始化完美反彈測試...');
            
            try {
                game = new Phaser.Game(gameConfig);
                scene = game.scene.scenes[0];
                log('完美反彈測試初始化成功');
            } catch (error) {
                log(`初始化失敗: ${error.message}`);
            }
        });
    </script>
</body>
</html>
