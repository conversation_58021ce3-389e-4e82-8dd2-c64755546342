/**
 * 主要遊戲啟動文件
 * Block Battle Arena - Main Game Entry Point
 */

// 遊戲配置
const gameConfig = {
    type: Phaser.AUTO,
    width: GAME_CONFIG.SCREEN.WIDTH,
    height: GAME_CONFIG.SCREEN.HEIGHT,
    parent: 'game-container',
    backgroundColor: GAME_CONFIG.SCREEN.BACKGROUND_COLOR,

    // 物理引擎配置
    physics: {
        default: 'matter',
        matter: {
            gravity: GAME_CONFIG.PHYSICS.GRAVITY,
            debug: GAME_CONFIG.DEBUG.SHOW_PHYSICS,
            debugBodyColor: 0x00ff00,
            debugBodyFillColor: 0x00ff00,
            debugStaticBodyColor: 0xff0000,
            debugVelocityColor: 0x00ffff,
            debugAngleColor: 0xffff00
        }
    },

    // 場景配置
    scene: [MenuScene, SkillSelectScene, GameScene, GameOverScene],

    // 輸入配置
    input: {
        keyboard: true,
        mouse: true,
        touch: true
    },

    // 渲染配置
    render: {
        antialias: true,
        pixelArt: false,
        roundPixels: false
    },

    // 音頻配置
    audio: {
        disableWebAudio: false
    },

    // 縮放配置
    scale: {
        mode: Phaser.Scale.FIT,
        autoCenter: Phaser.Scale.CENTER_BOTH,
        min: {
            width: 800,
            height: 600
        },
        max: {
            width: 1600,
            height: 1200
        }
    }
};

// 遊戲實例
let game;

// 遊戲初始化
function initGame() {
    console.log('Initializing Block Battle Arena...');

    try {
        // 檢查瀏覽器支援
        if (!checkBrowserSupport()) {
            showBrowserError();
            return;
        }

        // 檢查必要的類是否已定義
        if (typeof GAME_CONFIG === 'undefined') {
            throw new Error('GAME_CONFIG is not defined');
        }

        if (typeof MenuScene === 'undefined') {
            throw new Error('MenuScene is not defined');
        }

        // 創建遊戲實例
        game = new Phaser.Game(gameConfig);

        // 設置全域事件監聽
        setupGlobalEvents();

        // 設置性能監控
        if (GAME_CONFIG.DEBUG && GAME_CONFIG.DEBUG.SHOW_FPS) {
            setupPerformanceMonitor();
        }

        console.log('Game initialized successfully');

        // 使用 Utils.log 如果可用
        if (typeof Utils !== 'undefined') {
            Utils.log('Game initialized successfully');
        }

    } catch (error) {
        console.error(`Game initialization failed: ${error.message}`);
        showInitError(error);
    }
}

// 檢查瀏覽器支援
function checkBrowserSupport() {
    // 檢查 WebGL 支援
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

    if (!gl) {
        console.warn('WebGL not supported');
        return false;
    }

    // 檢查 Web Audio API 支援
    if (!window.AudioContext && !window.webkitAudioContext) {
        console.warn('Web Audio API not supported');
    }

    // 檢查 localStorage 支援
    if (!window.localStorage) {
        console.warn('localStorage not supported');
    }

    return true;
}

// 設置全域事件監聽
function setupGlobalEvents() {
    // 視窗大小變化
    window.addEventListener('resize', Utils.debounce(() => {
        if (game && game.scale) {
            game.scale.refresh();
        }
    }, 250));

    // 頁面可見性變化
    document.addEventListener('visibilitychange', () => {
        if (game && game.scene) {
            const activeScene = game.scene.getScene('GameScene');
            if (activeScene && activeScene.scene.isActive()) {
                if (document.hidden) {
                    activeScene.pauseGame();
                } else {
                    activeScene.resumeGame();
                }
            }
        }
    });

    // 錯誤處理
    window.addEventListener('error', (event) => {
        const errorMessage = event.error ? event.error.message : 'Unknown error';
        console.error(`Global error: ${errorMessage}`);
    });

    // 未處理的 Promise 拒絕
    window.addEventListener('unhandledrejection', (event) => {
        console.error(`Unhandled promise rejection: ${event.reason}`);
    });
}

// 設置性能監控
function setupPerformanceMonitor() {
    let lastTime = performance.now();
    let frameCount = 0;
    let fps = 0;

    // 創建 FPS 顯示元素
    const fpsDisplay = document.createElement('div');
    fpsDisplay.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 5px 10px;
        font-family: monospace;
        font-size: 12px;
        z-index: 10000;
        border-radius: 3px;
    `;
    document.body.appendChild(fpsDisplay);

    function updateFPS() {
        frameCount++;
        const currentTime = performance.now();

        if (currentTime >= lastTime + 1000) {
            fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
            frameCount = 0;
            lastTime = currentTime;

            fpsDisplay.textContent = `FPS: ${fps}`;

            // 性能警告
            if (fps < 30) {
                fpsDisplay.style.color = '#ff0000';
            } else if (fps < 50) {
                fpsDisplay.style.color = '#ffff00';
            } else {
                fpsDisplay.style.color = '#00ff00';
            }
        }

        requestAnimationFrame(updateFPS);
    }

    requestAnimationFrame(updateFPS);
}

// 顯示瀏覽器錯誤
function showBrowserError() {
    const errorDiv = document.createElement('div');
    errorDiv.innerHTML = `
        <div style="
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #ff0000;
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            font-family: Arial, sans-serif;
            z-index: 10000;
        ">
            <h2>瀏覽器不支援</h2>
            <p>您的瀏覽器不支援此遊戲所需的功能。</p>
            <p>請使用現代瀏覽器如 Chrome、Firefox 或 Safari。</p>
        </div>
    `;
    document.body.appendChild(errorDiv);
}

// 顯示初始化錯誤
function showInitError(error) {
    const errorDiv = document.createElement('div');
    errorDiv.innerHTML = `
        <div style="
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #ff0000;
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            font-family: Arial, sans-serif;
            z-index: 10000;
        ">
            <h2>遊戲載入失敗</h2>
            <p>遊戲初始化時發生錯誤：</p>
            <p><code>${error.message}</code></p>
            <button onclick="location.reload()" style="
                margin-top: 10px;
                padding: 10px 20px;
                background: white;
                color: #ff0000;
                border: none;
                border-radius: 5px;
                cursor: pointer;
            ">重新載入</button>
        </div>
    `;
    document.body.appendChild(errorDiv);
}

// 遊戲控制函數
window.GameControl = {
    // 暫停遊戲
    pause: function() {
        if (game && game.scene) {
            const activeScene = game.scene.getScene('GameScene');
            if (activeScene && activeScene.scene.isActive()) {
                activeScene.pauseGame();
            }
        }
    },

    // 恢復遊戲
    resume: function() {
        if (game && game.scene) {
            const activeScene = game.scene.getScene('GameScene');
            if (activeScene && activeScene.scene.isActive()) {
                activeScene.resumeGame();
            }
        }
    },

    // 重置遊戲
    restart: function() {
        if (game && game.scene) {
            const activeScene = game.scene.getScene('GameScene');
            if (activeScene) {
                activeScene.resetGame();
            }
        }
    },

    // 切換調試模式
    toggleDebug: function() {
        GAME_CONFIG.DEBUG.SHOW_PHYSICS = !GAME_CONFIG.DEBUG.SHOW_PHYSICS;
        if (game && game.scene) {
            const activeScene = game.scene.getScene('GameScene');
            if (activeScene && activeScene.matter) {
                activeScene.matter.world.debugGraphic.visible = GAME_CONFIG.DEBUG.SHOW_PHYSICS;
            }
        }
    },

    // 獲取遊戲統計
    getStats: function() {
        if (game && game.scene) {
            const activeScene = game.scene.getScene('GameScene');
            if (activeScene && activeScene.player1 && activeScene.player2) {
                return {
                    player1: {
                        hp: activeScene.player1.currentHP,
                        mode: activeScene.player1.mode,
                        skills: activeScene.player1.skillManager.getStats()
                    },
                    player2: {
                        hp: activeScene.player2.currentHP,
                        mode: activeScene.player2.mode,
                        skills: activeScene.player2.skillManager.getStats()
                    }
                };
            }
        }
        return null;
    }
};

// 當頁面載入完成時初始化遊戲
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, starting game initialization...');
    initGame();
});

// 導出遊戲實例 (用於調試)
window.game = game;
