/**
 * 玩家實體類
 * Block Battle Arena - Player Entity
 */

class Player {
    constructor(scene, x, y, playerId, customSkills = null) {
        this.scene = scene;
        this.playerId = playerId;
        this.isPlayer1 = playerId === 1;

        // 基本屬性
        this.maxHP = GAME_CONFIG.PLAYER.MAX_HP;
        this.currentHP = this.maxHP;
        this.size = GAME_CONFIG.PLAYER.SIZE;
        this.baseSpeed = GAME_CONFIG.PLAYER.BASE_SPEED;
        this.currentSpeed = this.baseSpeed;

        // 戰鬥模式
        this.mode = PLAYER_MODES.NEUTRAL;
        this.modeEffects = {};

        // 狀態效果
        this.effects = {
            shield: null,
            boost: null,
            reflectionField: null,
            ghostMode: null,
            invulnerable: false,
            stunned: false,
            frozen: false
        };

        // 技能配置
        this.customSkills = customSkills || ['SHOCKWAVE', 'SHIELD', 'TELEPORT', 'BOOST'];

        // 創建物理實體
        this.createPhysicsBody(x, y);

        // 創建視覺實體
        this.createVisuals();

        // 初始化技能管理器
        this.skillManager = new SkillManager(this, this.customSkills);

        // 粒子效果
        this.particles = [];
        this.trailPoints = [];

        // 輸入處理
        this.setupInput();

        // 事件監聽
        this.setupEvents();

        Utils.log(`Player ${playerId} created at (${x}, ${y}) with skills: ${this.customSkills.join(', ')}`);
    }

    createPhysicsBody(x, y) {
        // 使用 Matter.js 創建物理實體
        this.body = this.scene.matter.add.rectangle(x, y, this.size, this.size, {
            frictionAir: GAME_CONFIG.PHYSICS.FRICTION_AIR,
            friction: GAME_CONFIG.PHYSICS.FRICTION,
            restitution: GAME_CONFIG.PHYSICS.BOUNCE,
            density: GAME_CONFIG.PHYSICS.DENSITY,
            collisionFilter: {
                category: COLLISION_CATEGORIES.PLAYER,
                mask: COLLISION_CATEGORIES.PLAYER | COLLISION_CATEGORIES.WALL | COLLISION_CATEGORIES.PROJECTILE
            }
        });

        // 設置初始速度（包含垂直分量以確保上下反彈）
        const baseAngle = this.isPlayer1 ? 0.3 : Math.PI - 0.3; // 添加一些角度
        const velocity = {
            x: Math.cos(baseAngle) * this.baseSpeed / 60,
            y: Math.sin(baseAngle) * this.baseSpeed / 60
        };
        this.scene.matter.body.setVelocity(this.body, velocity);

        // 綁定玩家數據到物理實體（使用自定義屬性避免與 Phaser 衝突）
        this.body.playerData = this;
        this.body.playerId = this.playerId;
        this.body.entityType = 'player';
    }

    createVisuals() {
        const x = this.body.position.x;
        const y = this.body.position.y;
        const playerColor = this.isPlayer1 ? 0x00ffff : 0xff3366;

        // 創建現代化玩家設計
        this.createModernPlayerDesign(x, y, playerColor);

        // 創建UI元素
        this.createPlayerUI(x, y, playerColor);

        // 添加動畫效果
        this.addModernAnimations();
    }

    createModernPlayerDesign(x, y, playerColor) {
        // 外層光暈
        this.outerGlow = this.scene.add.circle(x, y, this.size * 0.9, playerColor, 0.1);

        // 主體 - 圓潤的圓形設計
        this.sprite = this.scene.add.circle(x, y, this.size * 0.5, playerColor, 0.95);
        this.sprite.setStrokeStyle(4, 0xffffff, 0.9);

        // 內層漸變圓
        this.innerLayer = this.scene.add.circle(x, y, this.size * 0.4, 0xffffff, 0.2);

        // 核心發光點
        this.core = this.scene.add.circle(x, y, this.size * 0.15, 0xffffff, 1);

        // 裝飾環
        this.decorRing1 = this.scene.add.circle(x, y, this.size * 0.35, 0x000000, 0);
        this.decorRing1.setStrokeStyle(2, playerColor, 0.7);

        this.decorRing2 = this.scene.add.circle(x, y, this.size * 0.25, 0x000000, 0);
        this.decorRing2.setStrokeStyle(1, 0xffffff, 0.5);

        // 能量點（4個小圓點）
        this.energyDots = [];
        for (let i = 0; i < 4; i++) {
            const angle = (i * Math.PI) / 2;
            const dotX = x + Math.cos(angle) * (this.size * 0.3);
            const dotY = y + Math.sin(angle) * (this.size * 0.3);

            const dot = this.scene.add.circle(dotX, dotY, 3, playerColor, 0.8);
            dot.setStrokeStyle(1, 0xffffff, 0.6);
            this.energyDots.push(dot);
        }

        // 模式指示器（外圈）
        this.modeIndicator = this.scene.add.circle(x, y, this.size * 0.6, 0x000000, 0);
        this.modeIndicator.setStrokeStyle(3, 0xffffff, 0.4);
    }

    createPlayerUI(x, y, playerColor) {
        // 現代化血量條
        this.healthBarContainer = this.scene.add.graphics();
        this.updateHealthBar();

        // 方向指示器 - 三角形箭頭
        this.directionArrow = this.scene.add.triangle(
            x, y - this.size * 0.7,
            0, -8,
            -6, 6,
            6, 6,
            0xffffff, 0.8
        );

        // 軌跡效果
        this.trailGraphics = this.scene.add.graphics();
    }

    updateHealthBar() {
        if (!this.healthBarContainer) return;

        this.healthBarContainer.clear();

        const x = this.body.position.x;
        const y = this.body.position.y - this.size - 20;
        const width = this.size + 20;
        const height = 6;
        const healthPercent = this.currentHP / this.maxHP;

        // 背景
        this.healthBarContainer.fillStyle(0x000000, 0.7);
        this.healthBarContainer.fillRoundedRect(x - width/2, y - height/2, width, height, 3);

        // 邊框
        this.healthBarContainer.lineStyle(1, 0xffffff, 0.5);
        this.healthBarContainer.strokeRoundedRect(x - width/2, y - height/2, width, height, 3);

        // 血量填充
        let healthColor = 0x00ff00; // 綠色
        if (healthPercent < 0.6) healthColor = 0xffaa00; // 橙色
        if (healthPercent < 0.3) healthColor = 0xff0000; // 紅色

        this.healthBarContainer.fillStyle(healthColor, 0.8);
        this.healthBarContainer.fillRoundedRect(
            x - width/2 + 1,
            y - height/2 + 1,
            (width - 2) * healthPercent,
            height - 2,
            2
        );
    }

    addModernAnimations() {
        // 外層光暈脈衝
        this.scene.tweens.add({
            targets: this.outerGlow,
            scaleX: 1.3,
            scaleY: 1.3,
            alpha: 0.05,
            duration: 2000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });

        // 主體輕微呼吸效果
        this.scene.tweens.add({
            targets: this.sprite,
            scaleX: 1.05,
            scaleY: 1.05,
            duration: 3000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });

        // 內層旋轉
        this.scene.tweens.add({
            targets: this.innerLayer,
            rotation: Math.PI * 2,
            duration: 8000,
            repeat: -1,
            ease: 'Linear'
        });

        // 核心脈衝
        this.scene.tweens.add({
            targets: this.core,
            scaleX: 1.4,
            scaleY: 1.4,
            alpha: 0.6,
            duration: 1500,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });

        // 裝飾環旋轉
        this.scene.tweens.add({
            targets: this.decorRing1,
            rotation: Math.PI * 2,
            duration: 6000,
            repeat: -1,
            ease: 'Linear'
        });

        this.scene.tweens.add({
            targets: this.decorRing2,
            rotation: -Math.PI * 2,
            duration: 4000,
            repeat: -1,
            ease: 'Linear'
        });

        // 能量點閃爍
        this.energyDots.forEach((dot, index) => {
            this.scene.tweens.add({
                targets: dot,
                alpha: 0.3,
                scaleX: 1.5,
                scaleY: 1.5,
                duration: 1000 + index * 200,
                yoyo: true,
                repeat: -1,
                ease: 'Sine.easeInOut'
            });
        });

        // 模式指示器脈衝
        this.scene.tweens.add({
            targets: this.modeIndicator,
            scaleX: 1.1,
            scaleY: 1.1,
            duration: 2500,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
    }

    setupInput() {
        // 玩家1控制
        if (this.isPlayer1) {
            this.keys = {
                modeSwitch: this.scene.input.keyboard.addKey('SPACE'),
                skill1: this.scene.input.keyboard.addKey('Q'),
                skill2: this.scene.input.keyboard.addKey('W'),
                skill3: this.scene.input.keyboard.addKey('E'),
                skill4: this.scene.input.keyboard.addKey('R')
            };
        } else {
            // 玩家2控制 (AI 或第二玩家)
            this.keys = {
                modeSwitch: this.scene.input.keyboard.addKey('ENTER'),
                skill1: this.scene.input.keyboard.addKey('U'),
                skill2: this.scene.input.keyboard.addKey('I'),
                skill3: this.scene.input.keyboard.addKey('O'),
                skill4: this.scene.input.keyboard.addKey('P')
            };
        }
    }

    setupEvents() {
        // 碰撞事件將在 GameScene 中統一處理
        // 這裡不需要重複設置
    }

    update(deltaTime) {
        // 優先處理輸入
        this.handleInput();

        // 應用模式行為（在物理更新前）
        this.applyModeAI();

        // 更新模式效果（可能影響速度）
        this.updateModeEffects(deltaTime);

        // 手動反彈檢查（在每幀的最後進行，確保即時反應）
        this.checkManualBounce();

        // 更新物理位置到視覺元素
        this.updateVisuals();

        // 更新狀態效果
        this.updateStatusEffects(deltaTime);

        // 更新技能
        this.skillManager.update(deltaTime);

        // 更新粒子效果
        Utils.updateParticles(this.particles, deltaTime);

        // 更新軌跡
        this.updateTrail();
    }

    checkManualBounce() {
        if (!this.body) return;

        const bounds = GAME_CONFIG.ARENA.BOUNDS;
        const halfSize = GAME_CONFIG.PLAYER.SIZE / 2;
        const position = this.body.position;
        const velocity = this.body.velocity;

        // 增加邊界檢測的提前量，確保即時反彈
        const bounceMargin = 2; // 提前2像素檢測

        let bounced = false;
        let newVelocity = { x: velocity.x, y: velocity.y };
        let newPosition = { x: position.x, y: position.y };

        // 檢查左右邊界（提前檢測）
        if (position.x - halfSize <= bounds.LEFT + bounceMargin) {
            newPosition.x = bounds.LEFT + halfSize + 1; // 稍微遠離邊界
            if (velocity.x < 0) { // 只有向左移動時才反彈
                newVelocity.x = Math.abs(velocity.x);
                bounced = true;
            }
        } else if (position.x + halfSize >= bounds.RIGHT - bounceMargin) {
            newPosition.x = bounds.RIGHT - halfSize - 1; // 稍微遠離邊界
            if (velocity.x > 0) { // 只有向右移動時才反彈
                newVelocity.x = -Math.abs(velocity.x);
                bounced = true;
            }
        }

        // 檢查上下邊界（提前檢測）
        if (position.y - halfSize <= bounds.TOP + bounceMargin) {
            newPosition.y = bounds.TOP + halfSize + 1; // 稍微遠離邊界
            if (velocity.y < 0) { // 只有向上移動時才反彈
                newVelocity.y = Math.abs(velocity.y);
                bounced = true;
            }
        } else if (position.y + halfSize >= bounds.BOTTOM - bounceMargin) {
            newPosition.y = bounds.BOTTOM - halfSize - 1; // 稍微遠離邊界
            if (velocity.y > 0) { // 只有向下移動時才反彈
                newVelocity.y = -Math.abs(velocity.y);
                bounced = true;
            }
        }

        // 如果發生反彈，立即設置新位置和速度
        if (bounced) {
            this.scene.matter.body.setPosition(this.body, newPosition);
            this.scene.matter.body.setVelocity(this.body, newVelocity);

            // 添加反彈效果（簡化版本）
            try {
                this.createBounceEffect();
            } catch (error) {
                console.warn('Bounce effect error:', error);
            }

            Utils.log(`Player ${this.playerId} instant bounce: velocity(${newVelocity.x.toFixed(1)}, ${newVelocity.y.toFixed(1)})`);
        }
    }

    createBounceEffect() {
        // 簡化的反彈效果，避免錯誤
        try {
            // 創建反彈粒子效果
            if (typeof Utils !== 'undefined' && Utils.createParticleData) {
                const particles = Utils.createParticleData(
                    this.body.position.x,
                    this.body.position.y,
                    5,
                    '#ffffff'
                );
                this.particles.push(...particles);
            }

            // 簡單的視覺反饋 - 改變透明度
            if (this.sprite && this.sprite.setAlpha) {
                const originalAlpha = this.sprite.alpha;
                this.sprite.setAlpha(0.5);
                setTimeout(() => {
                    if (this.sprite && this.sprite.setAlpha) {
                        this.sprite.setAlpha(originalAlpha);
                    }
                }, 100);
            }
        } catch (error) {
            console.warn('Bounce effect error:', error);
        }
    }

    updateVisuals() {
        const pos = this.body.position;

        // 更新所有視覺元素位置
        this.sprite.setPosition(pos.x, pos.y);
        this.outerGlow.setPosition(pos.x, pos.y);
        this.innerLayer.setPosition(pos.x, pos.y);
        this.core.setPosition(pos.x, pos.y);
        this.decorRing1.setPosition(pos.x, pos.y);
        this.decorRing2.setPosition(pos.x, pos.y);
        this.modeIndicator.setPosition(pos.x, pos.y);

        // 更新能量點位置
        this.energyDots.forEach((dot, index) => {
            const angle = (index * Math.PI) / 2;
            const dotX = pos.x + Math.cos(angle) * (this.size * 0.3);
            const dotY = pos.y + Math.sin(angle) * (this.size * 0.3);
            dot.setPosition(dotX, dotY);
        });

        // 更新方向箭頭
        const velocity = this.body.velocity;
        const angle = Math.atan2(velocity.y, velocity.x);
        this.directionArrow.setPosition(pos.x, pos.y - this.size * 0.7);
        this.directionArrow.setRotation(angle + Math.PI / 2);

        // 更新血量條
        this.updateHealthBar();

        // 更新模式指示器顏色
        const modeColor = GAME_CONFIG.MODES[this.mode.toUpperCase()].COLOR;
        this.modeIndicator.setStrokeStyle(3, parseInt(modeColor.replace('#', '0x')), 0.6);

        // 根據速度調整視覺效果
        const speed = Utils.vectorLength(velocity.x, velocity.y) * 60;
        const speedIntensity = Math.min(speed / this.currentSpeed, 1);

        // 速度越快，光暈越亮
        this.outerGlow.setAlpha(0.1 + speedIntensity * 0.2);
        this.core.setAlpha(0.8 + speedIntensity * 0.2);

        // 根據血量調整主體透明度
        const healthPercent = this.currentHP / this.maxHP;
        if (healthPercent < 0.3) {
            // 低血量時閃爍
            this.sprite.setAlpha(0.7 + 0.3 * Math.sin(Date.now() * 0.01));
        } else {
            this.sprite.setAlpha(0.95);
        }

        // 更新軌跡效果
        this.updateModernTrail();
    }

    updateModernTrail() {
        if (!this.trailGraphics) return;

        this.trailGraphics.clear();

        if (this.trailPoints.length > 1) {
            const playerColor = this.isPlayer1 ? 0x00ffff : 0xff3366;

            for (let i = 1; i < this.trailPoints.length; i++) {
                const point = this.trailPoints[i];
                const prevPoint = this.trailPoints[i - 1];
                const age = Date.now() - point.time;
                const maxAge = 800;
                const alpha = Math.max(0, 1 - age / maxAge) * 0.4;

                if (alpha > 0) {
                    // 漸變軌跡效果
                    const thickness = 4 * alpha;
                    this.trailGraphics.lineStyle(thickness, playerColor, alpha);
                    this.trailGraphics.moveTo(prevPoint.x, prevPoint.y);
                    this.trailGraphics.lineTo(point.x, point.y);
                    this.trailGraphics.strokePath();

                    // 內部發光軌跡
                    this.trailGraphics.lineStyle(thickness * 0.5, 0xffffff, alpha * 0.6);
                    this.trailGraphics.moveTo(prevPoint.x, prevPoint.y);
                    this.trailGraphics.lineTo(point.x, point.y);
                    this.trailGraphics.strokePath();
                }
            }
        }
    }

    updateModeEffects(deltaTime) {
        // 根據當前模式應用效果
        const modeConfig = GAME_CONFIG.MODES[this.mode.toUpperCase()];
        this.currentSpeed = this.baseSpeed * modeConfig.SPEED_MODIFIER;

        // 更新物理實體速度限制
        const currentVelocity = this.body.velocity;
        const currentSpeed = Utils.vectorLength(currentVelocity.x, currentVelocity.y) * 60;

        if (currentSpeed > this.currentSpeed) {
            const normalized = Utils.normalize(currentVelocity.x, currentVelocity.y);
            this.scene.matter.body.setVelocity(this.body, {
                x: normalized.x * this.currentSpeed / 60,
                y: normalized.y * this.currentSpeed / 60
            });
        }
    }

    updateStatusEffects(deltaTime) {
        // 更新護盾效果
        if (this.effects.shield) {
            this.effects.shield.duration -= deltaTime;
            if (this.effects.shield.duration <= 0) {
                this.removeShield();
            }
        }

        // 更新加速效果
        if (this.effects.boost) {
            this.effects.boost.duration -= deltaTime;
            if (this.effects.boost.duration <= 0) {
                this.removeBoost();
            }
        }

        // 更新無敵效果
        if (this.effects.invulnerable) {
            this.effects.invulnerableTime -= deltaTime;
            if (this.effects.invulnerableTime <= 0) {
                this.effects.invulnerable = false;
                this.sprite.setAlpha(1);
            } else {
                // 閃爍效果
                this.sprite.setAlpha(0.5 + 0.5 * Math.sin(Date.now() * 0.01));
            }
        }
    }

    handleInput() {
        // 模式切換
        if (Phaser.Input.Keyboard.JustDown(this.keys.modeSwitch)) {
            this.switchMode();
        }

        // 技能使用 - 動態基於玩家的技能配置
        if (Phaser.Input.Keyboard.JustDown(this.keys.skill1) && this.customSkills[0]) {
            this.skillManager.useSkill(this.customSkills[0].toLowerCase());
        }
        if (Phaser.Input.Keyboard.JustDown(this.keys.skill2) && this.customSkills[1]) {
            this.skillManager.useSkill(this.customSkills[1].toLowerCase());
        }
        if (Phaser.Input.Keyboard.JustDown(this.keys.skill3) && this.customSkills[2]) {
            this.skillManager.useSkill(this.customSkills[2].toLowerCase());
        }
        if (Phaser.Input.Keyboard.JustDown(this.keys.skill4) && this.customSkills[3]) {
            this.skillManager.useSkill(this.customSkills[3].toLowerCase());
        }
    }

    switchMode() {
        const modes = [PLAYER_MODES.NEUTRAL, PLAYER_MODES.ATTACK, PLAYER_MODES.DEFENSE];
        const currentIndex = modes.indexOf(this.mode);
        const nextIndex = (currentIndex + 1) % modes.length;
        this.mode = modes[nextIndex];

        // 播放模式切換音效
        if (this.scene.audioManager) {
            this.scene.audioManager.playSound('mode_switch', 0.7);
        }

        // 觸發模式切換事件
        this.scene.events.emit(EVENTS.MODE_CHANGED, this.playerId, this.mode);

        Utils.log(`Player ${this.playerId} switched to ${this.mode} mode`);
    }

    applyModeAI() {
        if (!this.scene.player2 || this.playerId === 2) return;

        const otherPlayer = this.playerId === 1 ? this.scene.player2 : this.scene.player1;
        const distance = Utils.distance(
            this.body.position.x, this.body.position.y,
            otherPlayer.body.position.x, otherPlayer.body.position.y
        );

        const currentVelocity = this.body.velocity;
        let targetDirection = { x: currentVelocity.x, y: currentVelocity.y };

        if (this.mode === PLAYER_MODES.ATTACK) {
            // 進攻模式：朝向對手
            const angle = Utils.angle(
                this.body.position.x, this.body.position.y,
                otherPlayer.body.position.x, otherPlayer.body.position.y
            );
            targetDirection = {
                x: Math.cos(angle),
                y: Math.sin(angle)
            };
        } else if (this.mode === PLAYER_MODES.DEFENSE) {
            // 防守模式：遠離對手
            const angle = Utils.angle(
                otherPlayer.body.position.x, otherPlayer.body.position.y,
                this.body.position.x, this.body.position.y
            );
            targetDirection = {
                x: Math.cos(angle),
                y: Math.sin(angle)
            };
        }

        // 平滑轉向
        const lerpFactor = 0.02;
        const newVelocity = {
            x: Utils.lerp(currentVelocity.x, targetDirection.x * this.currentSpeed / 60, lerpFactor),
            y: Utils.lerp(currentVelocity.y, targetDirection.y * this.currentSpeed / 60, lerpFactor)
        };

        this.scene.matter.body.setVelocity(this.body, newVelocity);
    }

    updateTrail() {
        // 添加當前位置到軌跡
        this.trailPoints.push({
            x: this.body.position.x,
            y: this.body.position.y,
            time: Date.now()
        });

        // 移除過舊的軌跡點
        const maxAge = 1000; // 1秒
        const now = Date.now();
        this.trailPoints = this.trailPoints.filter(point => now - point.time < maxAge);
    }

    takeDamage(amount, source = null) {
        if (this.effects.invulnerable) return false;

        // 計算實際傷害
        let actualDamage = amount;

        // 防守模式減傷
        if (this.mode === PLAYER_MODES.DEFENSE) {
            actualDamage *= GAME_CONFIG.MODES.DEFENSE.DAMAGE_REDUCTION;
        }

        // 護盾吸收
        if (this.effects.shield) {
            const absorbed = Math.min(actualDamage, this.effects.shield.absorption);
            this.effects.shield.absorption -= absorbed;
            actualDamage -= absorbed;

            if (this.effects.shield.absorption <= 0) {
                this.removeShield();
            }
        }

        // 扣除生命值
        this.currentHP = Math.max(0, this.currentHP - actualDamage);

        // 創建傷害粒子效果
        this.createDamageEffect(actualDamage);

        // 觸發受傷事件
        this.scene.events.emit(EVENTS.PLAYER_DAMAGED, this.playerId, actualDamage, source);

        // 檢查死亡
        if (this.currentHP <= 0) {
            this.die();
        }

        Utils.log(`Player ${this.playerId} took ${actualDamage} damage (${this.currentHP}/${this.maxHP} HP remaining)`);
        return true;
    }

    heal(amount) {
        const oldHP = this.currentHP;
        this.currentHP = Math.min(this.maxHP, this.currentHP + amount);
        const actualHeal = this.currentHP - oldHP;

        if (actualHeal > 0) {
            this.createHealEffect(actualHeal);
            this.scene.events.emit(EVENTS.PLAYER_HEALED, this.playerId, actualHeal);
        }

        return actualHeal;
    }

    die() {
        Utils.log(`Player ${this.playerId} died`);
        this.scene.events.emit(EVENTS.GAME_OVER, this.playerId === 1 ? 2 : 1);
    }

    createDamageEffect(amount) {
        const particles = Utils.createParticleData(
            this.body.position.x,
            this.body.position.y,
            Math.min(amount, 20),
            '#ff0000'
        );
        this.particles.push(...particles);
    }

    createHealEffect(amount) {
        const particles = Utils.createParticleData(
            this.body.position.x,
            this.body.position.y,
            Math.min(amount, 15),
            '#00ff00'
        );
        this.particles.push(...particles);
    }

    applyShield(absorption, duration) {
        this.effects.shield = { absorption, duration };
        Utils.log(`Player ${this.playerId} gained shield (${absorption} absorption, ${duration}ms duration)`);
    }

    removeShield() {
        this.effects.shield = null;
        Utils.log(`Player ${this.playerId} lost shield`);
    }

    applyBoost(multiplier, duration) {
        this.effects.boost = { multiplier, duration };
        this.currentSpeed *= multiplier;
        Utils.log(`Player ${this.playerId} gained boost (${multiplier}x speed, ${duration}ms duration)`);
    }

    removeBoost() {
        if (this.effects.boost) {
            this.currentSpeed /= this.effects.boost.multiplier;
            this.effects.boost = null;
            Utils.log(`Player ${this.playerId} lost boost`);
        }
    }

    setInvulnerable(duration) {
        this.effects.invulnerable = true;
        this.effects.invulnerableTime = duration;
    }

    handleCollisionWith(otherBody) {
        // 處理與其他物體的碰撞
        if (otherBody && otherBody.entityType === 'player' && otherBody.playerData instanceof Player) {
            this.handlePlayerCollision(otherBody.playerData);
        }
    }

    handlePlayerCollision(otherPlayer) {
        // 計算碰撞傷害
        const velocity1 = Utils.vectorLength(this.body.velocity.x, this.body.velocity.y) * 60;
        const velocity2 = Utils.vectorLength(otherPlayer.body.velocity.x, otherPlayer.body.velocity.y) * 60;

        const damage1 = Utils.clamp(
            velocity1 / 20,
            GAME_CONFIG.PLAYER.COLLISION_DAMAGE.MIN,
            GAME_CONFIG.PLAYER.COLLISION_DAMAGE.MAX
        );

        const damage2 = Utils.clamp(
            velocity2 / 20,
            GAME_CONFIG.PLAYER.COLLISION_DAMAGE.MIN,
            GAME_CONFIG.PLAYER.COLLISION_DAMAGE.MAX
        );

        // 進攻模式額外傷害
        let finalDamage1 = damage1;
        let finalDamage2 = damage2;

        if (this.mode === PLAYER_MODES.ATTACK) {
            finalDamage1 += GAME_CONFIG.PLAYER.COLLISION_DAMAGE.MAX * 0.3;
        }
        if (otherPlayer.mode === PLAYER_MODES.ATTACK) {
            finalDamage2 += GAME_CONFIG.PLAYER.COLLISION_DAMAGE.MAX * 0.3;
        }

        // 造成傷害
        this.takeDamage(finalDamage2, otherPlayer);
        otherPlayer.takeDamage(finalDamage1, this);

        Utils.log(`Player collision: P${this.playerId} took ${finalDamage2}, P${otherPlayer.playerId} took ${finalDamage1}`, 'collision');
    }

    destroy() {
        // 清理資源
        if (this.sprite) this.sprite.destroy();
        if (this.modeGlow) this.modeGlow.destroy();
        if (this.healthBar) this.healthBar.destroy();
        if (this.healthBarBg) this.healthBarBg.destroy();
        if (this.directionArrow) this.directionArrow.destroy();
        if (this.body) this.scene.matter.world.remove(this.body);

        Utils.log(`Player ${this.playerId} destroyed`);
    }
}
