/**
 * 玩家實體類
 * Block Battle Arena - Player Entity
 */

class Player {
    constructor(scene, x, y, playerId, customSkills = null) {
        this.scene = scene;
        this.playerId = playerId;
        this.isPlayer1 = playerId === 1;

        // 基本屬性
        this.maxHP = GAME_CONFIG.PLAYER.MAX_HP;
        this.currentHP = this.maxHP;
        this.size = GAME_CONFIG.PLAYER.SIZE;
        this.baseSpeed = GAME_CONFIG.PLAYER.BASE_SPEED;
        this.currentSpeed = this.baseSpeed;

        // 戰鬥模式
        this.mode = PLAYER_MODES.NEUTRAL;
        this.modeEffects = {};

        // 狀態效果
        this.effects = {
            shield: null,
            boost: null,
            reflectionField: null,
            ghostMode: null,
            invulnerable: false,
            stunned: false,
            frozen: false
        };

        // 技能配置
        this.customSkills = customSkills || ['SHOCKWAVE', 'SHIELD', 'TELEPORT', 'BOOST'];

        // 創建物理實體
        this.createPhysicsBody(x, y);

        // 創建視覺實體
        this.createVisuals();

        // 初始化技能管理器
        this.skillManager = new SkillManager(this, this.customSkills);

        // 粒子效果
        this.particles = [];
        this.trailPoints = [];

        // 輸入處理
        this.setupInput();

        // 事件監聽
        this.setupEvents();

        Utils.log(`Player ${playerId} created at (${x}, ${y}) with skills: ${this.customSkills.join(', ')}`);
    }

    createPhysicsBody(x, y) {
        // 使用 Matter.js 創建物理實體
        this.body = this.scene.matter.add.rectangle(x, y, this.size, this.size, {
            frictionAir: GAME_CONFIG.PHYSICS.FRICTION_AIR,
            friction: GAME_CONFIG.PHYSICS.FRICTION,
            restitution: GAME_CONFIG.PHYSICS.BOUNCE,
            density: GAME_CONFIG.PHYSICS.DENSITY,
            collisionFilter: {
                category: COLLISION_CATEGORIES.PLAYER,
                mask: COLLISION_CATEGORIES.PLAYER | COLLISION_CATEGORIES.WALL | COLLISION_CATEGORIES.PROJECTILE
            }
        });

        // 設置初始速度
        const angle = this.isPlayer1 ? 0 : Math.PI;
        const velocity = {
            x: Math.cos(angle) * this.baseSpeed / 60,
            y: Math.sin(angle) * this.baseSpeed / 60
        };
        this.scene.matter.body.setVelocity(this.body, velocity);

        // 綁定玩家數據到物理實體
        this.body.gameObject = this;
        this.body.playerId = this.playerId;
    }

    createVisuals() {
        // 主要方塊
        this.sprite = this.scene.add.rectangle(
            this.body.position.x,
            this.body.position.y,
            this.size,
            this.size,
            this.isPlayer1 ? 0x00d4ff : 0xff6b35
        );
        this.sprite.setStrokeStyle(2, 0xffffff);

        // 模式指示光環
        this.modeGlow = this.scene.add.circle(
            this.body.position.x,
            this.body.position.y,
            this.size * 0.8,
            0xffffff,
            0.3
        );

        // 血量條 (在方塊上方)
        this.healthBarBg = this.scene.add.rectangle(
            this.body.position.x,
            this.body.position.y - this.size - 10,
            this.size + 10,
            6,
            0x000000,
            0.7
        );

        this.healthBar = this.scene.add.rectangle(
            this.body.position.x,
            this.body.position.y - this.size - 10,
            this.size + 10,
            6,
            this.isPlayer1 ? 0x00d4ff : 0xff6b35
        );

        // 方向指示器
        this.directionArrow = this.scene.add.triangle(
            this.body.position.x,
            this.body.position.y,
            0, -8,
            -6, 8,
            6, 8,
            0xffffff,
            0.8
        );
    }

    setupInput() {
        // 玩家1控制
        if (this.isPlayer1) {
            this.keys = {
                modeSwitch: this.scene.input.keyboard.addKey('SPACE'),
                skill1: this.scene.input.keyboard.addKey('Q'),
                skill2: this.scene.input.keyboard.addKey('W'),
                skill3: this.scene.input.keyboard.addKey('E'),
                skill4: this.scene.input.keyboard.addKey('R')
            };
        } else {
            // 玩家2控制 (AI 或第二玩家)
            this.keys = {
                modeSwitch: this.scene.input.keyboard.addKey('ENTER'),
                skill1: this.scene.input.keyboard.addKey('U'),
                skill2: this.scene.input.keyboard.addKey('I'),
                skill3: this.scene.input.keyboard.addKey('O'),
                skill4: this.scene.input.keyboard.addKey('P')
            };
        }
    }

    setupEvents() {
        // 碰撞事件將在 GameScene 中統一處理
        // 這裡不需要重複設置
    }

    update(deltaTime) {
        // 更新物理位置到視覺元素
        this.updateVisuals();

        // 更新模式效果
        this.updateModeEffects(deltaTime);

        // 更新狀態效果
        this.updateStatusEffects(deltaTime);

        // 更新技能
        this.skillManager.update(deltaTime);

        // 更新粒子效果
        Utils.updateParticles(this.particles, deltaTime);

        // 處理輸入
        this.handleInput();

        // 更新軌跡
        this.updateTrail();

        // 應用模式行為
        this.applyModeAI();
    }

    updateVisuals() {
        const pos = this.body.position;

        // 更新主要元素位置
        this.sprite.setPosition(pos.x, pos.y);
        this.modeGlow.setPosition(pos.x, pos.y);
        this.healthBarBg.setPosition(pos.x, pos.y - this.size - 10);
        this.healthBar.setPosition(pos.x, pos.y - this.size - 10);

        // 更新方向箭頭
        const velocity = this.body.velocity;
        const angle = Math.atan2(velocity.y, velocity.x);
        this.directionArrow.setPosition(pos.x, pos.y);
        this.directionArrow.setRotation(angle + Math.PI / 2);

        // 更新血量條
        const healthPercent = this.currentHP / this.maxHP;
        this.healthBar.scaleX = healthPercent;

        // 更新模式光環
        const modeColor = GAME_CONFIG.MODES[this.mode.toUpperCase()].COLOR;
        this.modeGlow.setFillStyle(parseInt(modeColor.replace('#', '0x')), 0.3);
    }

    updateModeEffects(deltaTime) {
        // 根據當前模式應用效果
        const modeConfig = GAME_CONFIG.MODES[this.mode.toUpperCase()];
        this.currentSpeed = this.baseSpeed * modeConfig.SPEED_MODIFIER;

        // 更新物理實體速度限制
        const currentVelocity = this.body.velocity;
        const currentSpeed = Utils.vectorLength(currentVelocity.x, currentVelocity.y) * 60;

        if (currentSpeed > this.currentSpeed) {
            const normalized = Utils.normalize(currentVelocity.x, currentVelocity.y);
            this.scene.matter.body.setVelocity(this.body, {
                x: normalized.x * this.currentSpeed / 60,
                y: normalized.y * this.currentSpeed / 60
            });
        }
    }

    updateStatusEffects(deltaTime) {
        // 更新護盾效果
        if (this.effects.shield) {
            this.effects.shield.duration -= deltaTime;
            if (this.effects.shield.duration <= 0) {
                this.removeShield();
            }
        }

        // 更新加速效果
        if (this.effects.boost) {
            this.effects.boost.duration -= deltaTime;
            if (this.effects.boost.duration <= 0) {
                this.removeBoost();
            }
        }

        // 更新無敵效果
        if (this.effects.invulnerable) {
            this.effects.invulnerableTime -= deltaTime;
            if (this.effects.invulnerableTime <= 0) {
                this.effects.invulnerable = false;
                this.sprite.setAlpha(1);
            } else {
                // 閃爍效果
                this.sprite.setAlpha(0.5 + 0.5 * Math.sin(Date.now() * 0.01));
            }
        }
    }

    handleInput() {
        // 模式切換
        if (Phaser.Input.Keyboard.JustDown(this.keys.modeSwitch)) {
            this.switchMode();
        }

        // 技能使用 - 動態基於玩家的技能配置
        if (Phaser.Input.Keyboard.JustDown(this.keys.skill1) && this.customSkills[0]) {
            this.skillManager.useSkill(this.customSkills[0].toLowerCase());
        }
        if (Phaser.Input.Keyboard.JustDown(this.keys.skill2) && this.customSkills[1]) {
            this.skillManager.useSkill(this.customSkills[1].toLowerCase());
        }
        if (Phaser.Input.Keyboard.JustDown(this.keys.skill3) && this.customSkills[2]) {
            this.skillManager.useSkill(this.customSkills[2].toLowerCase());
        }
        if (Phaser.Input.Keyboard.JustDown(this.keys.skill4) && this.customSkills[3]) {
            this.skillManager.useSkill(this.customSkills[3].toLowerCase());
        }
    }

    switchMode() {
        const modes = [PLAYER_MODES.NEUTRAL, PLAYER_MODES.ATTACK, PLAYER_MODES.DEFENSE];
        const currentIndex = modes.indexOf(this.mode);
        const nextIndex = (currentIndex + 1) % modes.length;
        this.mode = modes[nextIndex];

        // 播放模式切換音效
        if (this.scene.audioManager) {
            this.scene.audioManager.playSound('mode_switch', 0.7);
        }

        // 觸發模式切換事件
        this.scene.events.emit(EVENTS.MODE_CHANGED, this.playerId, this.mode);

        Utils.log(`Player ${this.playerId} switched to ${this.mode} mode`);
    }

    applyModeAI() {
        if (!this.scene.player2 || this.playerId === 2) return;

        const otherPlayer = this.playerId === 1 ? this.scene.player2 : this.scene.player1;
        const distance = Utils.distance(
            this.body.position.x, this.body.position.y,
            otherPlayer.body.position.x, otherPlayer.body.position.y
        );

        const currentVelocity = this.body.velocity;
        let targetDirection = { x: currentVelocity.x, y: currentVelocity.y };

        if (this.mode === PLAYER_MODES.ATTACK) {
            // 進攻模式：朝向對手
            const angle = Utils.angle(
                this.body.position.x, this.body.position.y,
                otherPlayer.body.position.x, otherPlayer.body.position.y
            );
            targetDirection = {
                x: Math.cos(angle),
                y: Math.sin(angle)
            };
        } else if (this.mode === PLAYER_MODES.DEFENSE) {
            // 防守模式：遠離對手
            const angle = Utils.angle(
                otherPlayer.body.position.x, otherPlayer.body.position.y,
                this.body.position.x, this.body.position.y
            );
            targetDirection = {
                x: Math.cos(angle),
                y: Math.sin(angle)
            };
        }

        // 平滑轉向
        const lerpFactor = 0.02;
        const newVelocity = {
            x: Utils.lerp(currentVelocity.x, targetDirection.x * this.currentSpeed / 60, lerpFactor),
            y: Utils.lerp(currentVelocity.y, targetDirection.y * this.currentSpeed / 60, lerpFactor)
        };

        this.scene.matter.body.setVelocity(this.body, newVelocity);
    }

    updateTrail() {
        // 添加當前位置到軌跡
        this.trailPoints.push({
            x: this.body.position.x,
            y: this.body.position.y,
            time: Date.now()
        });

        // 移除過舊的軌跡點
        const maxAge = 1000; // 1秒
        const now = Date.now();
        this.trailPoints = this.trailPoints.filter(point => now - point.time < maxAge);
    }

    takeDamage(amount, source = null) {
        if (this.effects.invulnerable) return false;

        // 計算實際傷害
        let actualDamage = amount;

        // 防守模式減傷
        if (this.mode === PLAYER_MODES.DEFENSE) {
            actualDamage *= GAME_CONFIG.MODES.DEFENSE.DAMAGE_REDUCTION;
        }

        // 護盾吸收
        if (this.effects.shield) {
            const absorbed = Math.min(actualDamage, this.effects.shield.absorption);
            this.effects.shield.absorption -= absorbed;
            actualDamage -= absorbed;

            if (this.effects.shield.absorption <= 0) {
                this.removeShield();
            }
        }

        // 扣除生命值
        this.currentHP = Math.max(0, this.currentHP - actualDamage);

        // 創建傷害粒子效果
        this.createDamageEffect(actualDamage);

        // 觸發受傷事件
        this.scene.events.emit(EVENTS.PLAYER_DAMAGED, this.playerId, actualDamage, source);

        // 檢查死亡
        if (this.currentHP <= 0) {
            this.die();
        }

        Utils.log(`Player ${this.playerId} took ${actualDamage} damage (${this.currentHP}/${this.maxHP} HP remaining)`);
        return true;
    }

    heal(amount) {
        const oldHP = this.currentHP;
        this.currentHP = Math.min(this.maxHP, this.currentHP + amount);
        const actualHeal = this.currentHP - oldHP;

        if (actualHeal > 0) {
            this.createHealEffect(actualHeal);
            this.scene.events.emit(EVENTS.PLAYER_HEALED, this.playerId, actualHeal);
        }

        return actualHeal;
    }

    die() {
        Utils.log(`Player ${this.playerId} died`);
        this.scene.events.emit(EVENTS.GAME_OVER, this.playerId === 1 ? 2 : 1);
    }

    createDamageEffect(amount) {
        const particles = Utils.createParticleData(
            this.body.position.x,
            this.body.position.y,
            Math.min(amount, 20),
            '#ff0000'
        );
        this.particles.push(...particles);
    }

    createHealEffect(amount) {
        const particles = Utils.createParticleData(
            this.body.position.x,
            this.body.position.y,
            Math.min(amount, 15),
            '#00ff00'
        );
        this.particles.push(...particles);
    }

    applyShield(absorption, duration) {
        this.effects.shield = { absorption, duration };
        Utils.log(`Player ${this.playerId} gained shield (${absorption} absorption, ${duration}ms duration)`);
    }

    removeShield() {
        this.effects.shield = null;
        Utils.log(`Player ${this.playerId} lost shield`);
    }

    applyBoost(multiplier, duration) {
        this.effects.boost = { multiplier, duration };
        this.currentSpeed *= multiplier;
        Utils.log(`Player ${this.playerId} gained boost (${multiplier}x speed, ${duration}ms duration)`);
    }

    removeBoost() {
        if (this.effects.boost) {
            this.currentSpeed /= this.effects.boost.multiplier;
            this.effects.boost = null;
            Utils.log(`Player ${this.playerId} lost boost`);
        }
    }

    setInvulnerable(duration) {
        this.effects.invulnerable = true;
        this.effects.invulnerableTime = duration;
    }

    handleCollisionWith(otherBody) {
        // 處理與其他物體的碰撞
        if (otherBody && otherBody.gameObject) {
            if (otherBody.gameObject instanceof Player) {
                this.handlePlayerCollision(otherBody.gameObject);
            }
        }
    }

    handlePlayerCollision(otherPlayer) {
        // 計算碰撞傷害
        const velocity1 = Utils.vectorLength(this.body.velocity.x, this.body.velocity.y) * 60;
        const velocity2 = Utils.vectorLength(otherPlayer.body.velocity.x, otherPlayer.body.velocity.y) * 60;

        const damage1 = Utils.clamp(
            velocity1 / 20,
            GAME_CONFIG.PLAYER.COLLISION_DAMAGE.MIN,
            GAME_CONFIG.PLAYER.COLLISION_DAMAGE.MAX
        );

        const damage2 = Utils.clamp(
            velocity2 / 20,
            GAME_CONFIG.PLAYER.COLLISION_DAMAGE.MIN,
            GAME_CONFIG.PLAYER.COLLISION_DAMAGE.MAX
        );

        // 進攻模式額外傷害
        let finalDamage1 = damage1;
        let finalDamage2 = damage2;

        if (this.mode === PLAYER_MODES.ATTACK) {
            finalDamage1 += GAME_CONFIG.PLAYER.COLLISION_DAMAGE.MAX * 0.3;
        }
        if (otherPlayer.mode === PLAYER_MODES.ATTACK) {
            finalDamage2 += GAME_CONFIG.PLAYER.COLLISION_DAMAGE.MAX * 0.3;
        }

        // 造成傷害
        this.takeDamage(finalDamage2, otherPlayer);
        otherPlayer.takeDamage(finalDamage1, this);

        Utils.log(`Player collision: P${this.playerId} took ${finalDamage2}, P${otherPlayer.playerId} took ${finalDamage1}`, 'collision');
    }

    destroy() {
        // 清理資源
        if (this.sprite) this.sprite.destroy();
        if (this.modeGlow) this.modeGlow.destroy();
        if (this.healthBar) this.healthBar.destroy();
        if (this.healthBarBg) this.healthBarBg.destroy();
        if (this.directionArrow) this.directionArrow.destroy();
        if (this.body) this.scene.matter.world.remove(this.body);

        Utils.log(`Player ${this.playerId} destroyed`);
    }
}
