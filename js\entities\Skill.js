/**
 * 技能實體類
 * Block Battle Arena - Skill Entities
 */

class Skill {
    constructor(scene, caster, skillType, targetX, targetY) {
        this.scene = scene;
        this.caster = caster;
        this.skillType = skillType;
        this.config = GAME_CONFIG.SKILLS[skillType.toUpperCase()];

        this.active = true;
        this.startTime = Date.now();

        // 根據技能類型創建不同的實體
        this.createSkillEntity(targetX, targetY);

        Utils.log(`${skillType} skill created by Player ${caster.playerId}`);
    }

    createSkillEntity(targetX, targetY) {
        switch (this.skillType.toLowerCase()) {
            case 'shockwave':
                this.createShockwave(targetX, targetY);
                break;
            case 'homing_missile':
                this.createHomingMissile(targetX, targetY);
                break;
            case 'explosive_bomb':
                this.createExplosiveBomb(targetX, targetY);
                break;
            case 'lightning_chain':
                this.createLightningChain(targetX, targetY);
                break;
            case 'shield':
                this.createShield();
                break;
            case 'reflection_field':
                this.createReflectionField();
                break;
            case 'ghost_mode':
                this.createGhostMode();
                break;
            case 'teleport':
                this.createTeleport(targetX, targetY);
                break;
            case 'boost':
                this.createBoost();
                break;
            case 'dash_strike':
                this.createDashStrike(targetX, targetY);
                break;
            case 'gravity_well':
                this.createGravityWell(targetX, targetY);
                break;
            case 'energy_wall':
                this.createEnergyWall(targetX, targetY);
                break;
            case 'freeze_ray':
                this.createFreezeRay(targetX, targetY);
                break;
        }
    }

    createShockwave(targetX, targetY) {
        const startX = this.caster.body.position.x;
        const startY = this.caster.body.position.y;

        // 計算方向
        const angle = Utils.angle(startX, startY, targetX, targetY);
        const velocity = {
            x: Math.cos(angle) * this.config.SPEED / 60,
            y: Math.sin(angle) * this.config.SPEED / 60
        };

        // 創建物理實體
        this.body = this.scene.matter.add.rectangle(
            startX + Math.cos(angle) * (this.caster.size + 10),
            startY + Math.sin(angle) * (this.caster.size + 10),
            this.config.WIDTH,
            this.config.WIDTH,
            {
                isSensor: true,
                collisionFilter: {
                    category: COLLISION_CATEGORIES.PROJECTILE,
                    mask: COLLISION_CATEGORIES.PLAYER | COLLISION_CATEGORIES.WALL
                }
            }
        );

        this.scene.matter.body.setVelocity(this.body, velocity);
        this.body.skillData = this;
        this.body.skillType = 'shockwave';
        this.body.entityType = 'skill';

        // 創建視覺效果
        this.sprite = this.scene.add.circle(
            this.body.position.x,
            this.body.position.y,
            this.config.WIDTH / 2,
            parseInt(this.config.COLOR.replace('#', '0x')),
            0.8
        );

        // 添加發光效果
        this.glow = this.scene.add.circle(
            this.body.position.x,
            this.body.position.y,
            this.config.WIDTH,
            parseInt(this.config.COLOR.replace('#', '0x')),
            0.3
        );

        // 設置生命週期
        this.maxDistance = this.config.RANGE;
        this.traveledDistance = 0;
        this.startPosition = { x: startX, y: startY };
    }

    createShield() {
        // 護盾是狀態效果，不需要物理實體
        this.caster.applyShield(this.config.ABSORPTION, this.config.DURATION);

        // 創建視覺效果
        this.sprite = this.scene.add.circle(
            this.caster.body.position.x,
            this.caster.body.position.y,
            this.caster.size + 10,
            parseInt(this.config.COLOR.replace('#', '0x')),
            0.4
        );
        this.sprite.setStrokeStyle(3, parseInt(this.config.COLOR.replace('#', '0x')));

        this.duration = this.config.DURATION;
    }

    createTeleport(targetX, targetY) {
        const startX = this.caster.body.position.x;
        const startY = this.caster.body.position.y;

        // 計算目標位置 (限制在範圍內)
        const distance = Utils.distance(startX, startY, targetX, targetY);
        const maxRange = this.config.RANGE;

        let finalX = targetX;
        let finalY = targetY;

        if (distance > maxRange) {
            const angle = Utils.angle(startX, startY, targetX, targetY);
            finalX = startX + Math.cos(angle) * maxRange;
            finalY = startY + Math.sin(angle) * maxRange;
        }

        // 確保不會傳送到牆內
        const bounds = GAME_CONFIG.ARENA.BOUNDS;
        finalX = Utils.clamp(finalX, bounds.LEFT + this.caster.size/2, bounds.RIGHT - this.caster.size/2);
        finalY = Utils.clamp(finalY, bounds.TOP + this.caster.size/2, bounds.BOTTOM - this.caster.size/2);

        // 創建傳送效果
        this.createTeleportEffect(startX, startY, finalX, finalY);

        // 延遲傳送
        this.castTime = this.config.CAST_TIME;
        this.targetPosition = { x: finalX, y: finalY };
        this.teleporting = false;
    }

    createTeleportEffect(startX, startY, endX, endY) {
        // 起點效果
        this.startEffect = this.scene.add.circle(
            startX, startY, 30,
            parseInt(this.config.COLOR.replace('#', '0x')),
            0.6
        );

        // 終點效果
        this.endEffect = this.scene.add.circle(
            endX, endY, 30,
            parseInt(this.config.COLOR.replace('#', '0x')),
            0.6
        );

        // 連接線
        this.connectionLine = this.scene.add.line(
            0, 0,
            startX, startY, endX, endY,
            parseInt(this.config.COLOR.replace('#', '0x')),
            0.4
        );
        this.connectionLine.setLineWidth(3);
    }

    createBoost() {
        // 加速是狀態效果
        this.caster.applyBoost(this.config.SPEED_MULTIPLIER, this.config.DURATION);

        // 創建視覺效果
        this.sprite = this.scene.add.circle(
            this.caster.body.position.x,
            this.caster.body.position.y,
            this.caster.size + 15,
            parseInt(this.config.COLOR.replace('#', '0x')),
            0.3
        );

        this.duration = this.config.DURATION;
        this.pulseTime = 0;
    }

    update(deltaTime) {
        if (!this.active) return;

        const currentTime = Date.now();
        const elapsed = currentTime - this.startTime;

        switch (this.skillType.toLowerCase()) {
            case 'shockwave':
                this.updateShockwave(deltaTime);
                break;
            case 'shield':
                this.updateShield(deltaTime);
                break;
            case 'teleport':
                this.updateTeleport(deltaTime, elapsed);
                break;
            case 'boost':
                this.updateBoost(deltaTime);
                break;
        }
    }

    updateShockwave(deltaTime) {
        if (!this.body) return;

        // 更新視覺位置
        this.sprite.setPosition(this.body.position.x, this.body.position.y);
        this.glow.setPosition(this.body.position.x, this.body.position.y);

        // 檢查飛行距離
        this.traveledDistance = Utils.distance(
            this.startPosition.x, this.startPosition.y,
            this.body.position.x, this.body.position.y
        );

        if (this.traveledDistance >= this.maxDistance) {
            this.destroy();
        }

        // 添加拖尾效果
        this.glow.setScale(1 + Math.sin(Date.now() * 0.01) * 0.2);
    }

    updateShield(deltaTime) {
        if (!this.caster.effects.shield) {
            this.destroy();
            return;
        }

        // 更新位置跟隨玩家
        this.sprite.setPosition(this.caster.body.position.x, this.caster.body.position.y);

        // 脈衝效果
        const pulse = 1 + Math.sin(Date.now() * 0.005) * 0.1;
        this.sprite.setScale(pulse);

        // 根據護盾值調整透明度
        const shieldPercent = this.caster.effects.shield.absorption / this.config.ABSORPTION;
        this.sprite.setAlpha(0.4 * shieldPercent);
    }

    updateTeleport(deltaTime, elapsed) {
        if (!this.teleporting && elapsed >= this.castTime) {
            // 執行傳送
            this.scene.matter.body.setPosition(this.caster.body, this.targetPosition);
            this.caster.setInvulnerable(this.config.INVULNERABILITY);
            this.teleporting = true;

            // 創建傳送完成效果
            this.createTeleportCompleteEffect();
        }

        if (this.teleporting && elapsed >= this.castTime + 500) {
            this.destroy();
        }

        // 更新視覺效果
        if (this.startEffect) {
            const pulse = 1 + Math.sin(elapsed * 0.01) * 0.3;
            this.startEffect.setScale(pulse);
            this.endEffect.setScale(pulse);
        }
    }

    createTeleportCompleteEffect() {
        // 在新位置創建爆炸效果
        const particles = Utils.createParticleData(
            this.targetPosition.x,
            this.targetPosition.y,
            20,
            this.config.COLOR
        );
        this.caster.particles.push(...particles);
    }

    updateBoost(deltaTime) {
        if (!this.caster.effects.boost) {
            this.destroy();
            return;
        }

        // 更新位置跟隨玩家
        this.sprite.setPosition(this.caster.body.position.x, this.caster.body.position.y);

        // 快速脈衝效果
        this.pulseTime += deltaTime;
        const pulse = 1 + Math.sin(this.pulseTime * 0.02) * 0.4;
        this.sprite.setScale(pulse);

        // 創建速度軌跡
        if (Math.random() < 0.3) {
            const particles = Utils.createParticleData(
                this.caster.body.position.x + Utils.randomBetween(-15, 15),
                this.caster.body.position.y + Utils.randomBetween(-15, 15),
                3,
                this.config.COLOR
            );
            this.caster.particles.push(...particles);
        }
    }

    onHit(target) {
        if (this.skillType.toLowerCase() === 'shockwave') {
            if (target instanceof Player && target !== this.caster) {
                // 計算傷害
                let damage = this.config.DAMAGE;

                // 進攻模式加成
                if (this.caster.mode === PLAYER_MODES.ATTACK) {
                    damage *= GAME_CONFIG.MODES.ATTACK.DAMAGE_MODIFIER;
                }

                target.takeDamage(damage, this.caster);
                this.destroy();
            }
        }
    }

    destroy() {
        this.active = false;

        // 清理物理實體
        if (this.body) {
            this.scene.matter.world.remove(this.body);
        }

        // 清理視覺元素
        if (this.sprite) this.sprite.destroy();
        if (this.glow) this.glow.destroy();
        if (this.startEffect) this.startEffect.destroy();
        if (this.endEffect) this.endEffect.destroy();
        if (this.connectionLine) this.connectionLine.destroy();

        Utils.log(`${this.skillType} skill destroyed`);
    }
}

// 技能工廠類
class SkillFactory {
    static createSkill(scene, caster, skillType, targetX, targetY) {
        return new Skill(scene, caster, skillType, targetX, targetY);
    }

    static getSkillConfig(skillType) {
        return GAME_CONFIG.SKILLS[skillType.toUpperCase()];
    }

    static isSkillAvailable(skillType) {
        return GAME_CONFIG.SKILLS.hasOwnProperty(skillType.toUpperCase());
    }
}
