/**
 * UI管理器
 * Block Battle Arena - UI Manager
 */

class UIManager {
    constructor(scene) {
        this.scene = scene;
        this.gameStartTime = Date.now();

        // UI元素引用
        this.elements = {
            player1Health: document.querySelector('.health-bar.player1 .health-fill'),
            player1HealthText: document.querySelector('.health-bar.player1 .health-text'),
            player2Health: document.querySelector('.health-bar.player2 .health-fill'),
            player2HealthText: document.querySelector('.health-bar.player2 .health-text'),
            gameTimer: document.querySelector('.game-timer'),
            modeIndicator: document.querySelector('.mode-indicator'),
            loadingScreen: document.querySelector('#loading-screen'),
            skillButtons: document.querySelectorAll('.skill-button')
        };

        // 綁定事件
        this.bindEvents();

        // 初始化技能按鈕
        this.initializeSkillButtons();

        Utils.log('UIManager initialized');
    }

    bindEvents() {
        // 監聽遊戲事件
        this.scene.events.on(EVENTS.PLAYER_DAMAGED, this.onPlayerDamaged.bind(this));
        this.scene.events.on(EVENTS.PLAYER_HEALED, this.onPlayerHealed.bind(this));
        this.scene.events.on(EVENTS.MODE_CHANGED, this.onModeChanged.bind(this));
        this.scene.events.on(EVENTS.SKILL_USED, this.onSkillUsed.bind(this));
        this.scene.events.on(EVENTS.GAME_OVER, this.onGameOver.bind(this));

        // 技能按鈕點擊事件
        this.elements.skillButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const skillName = button.getAttribute('data-skill');
                this.onSkillButtonClick(skillName);
            });
        });

        // 鍵盤快捷鍵
        document.addEventListener('keydown', this.onKeyDown.bind(this));
    }

    initializeSkillButtons() {
        // 設置技能按鈕的初始狀態和提示
        const skillConfigs = {
            shockwave: { name: '衝擊波', key: 'Q', description: '發射能量波攻擊敵人' },
            homing_missile: { name: '追蹤彈', key: 'Q', description: '發射追蹤導彈' },
            explosive_bomb: { name: '爆炸彈', key: 'Q', description: '投擲延時爆炸彈' },
            lightning_chain: { name: '雷電鏈', key: 'Q', description: '雷電鏈式攻擊' },
            shield: { name: '能量護盾', key: 'W', description: '創建護盾吸收傷害' },
            reflection_field: { name: '反射場', key: 'W', description: '反射敵方攻擊' },
            ghost_mode: { name: '幽靈化', key: 'W', description: '短暫無敵並可穿牆' },
            teleport: { name: '瞬移', key: 'E', description: '瞬間移動到目標位置' },
            boost: { name: '瞬間加速', key: 'R', description: '提升移動速度' },
            dash_strike: { name: '衝刺攻擊', key: 'E', description: '衝刺攻擊穿越敵人' },
            gravity_well: { name: '重力井', key: 'R', description: '創建重力井吸引物體' },
            energy_wall: { name: '能量牆', key: 'R', description: '創建能量牆阻擋移動' },
            freeze_ray: { name: '冰凍射線', key: 'R', description: '冰凍光束減緩敵人' }
        };

        // 基本設置
        this.elements.skillButtons.forEach((button, index) => {
            const skillName = button.getAttribute('data-skill');
            const config = skillConfigs[skillName];

            if (config) {
                button.title = `${config.name} (${config.key}) - ${config.description}`;
            }
        });

        // 延遲更新技能按鈕（等待玩家創建）
        setTimeout(() => {
            if (this.scene.player1 && this.scene.player1.customSkills) {
                this.updateSkillButtonsForPlayer(this.scene.player1.customSkills, skillConfigs);
            }
        }, 100);
    }

    updateSkillButtonsForPlayer(playerSkills, skillConfigs) {
        this.elements.skillButtons.forEach((button, index) => {
            if (index < playerSkills.length) {
                const skillKey = playerSkills[index].toLowerCase();
                const config = skillConfigs[skillKey];

                if (config) {
                    button.title = `${config.name} (${config.key}) - ${config.description}`;
                    button.querySelector('.skill-text') &&
                        (button.querySelector('.skill-text').textContent = config.key);
                }

                // 更新 data-skill 屬性
                button.setAttribute('data-skill', skillKey);
                button.style.display = 'flex';
            } else {
                // 隱藏多餘的技能按鈕
                button.style.display = 'none';
            }
        });
    }

    update() {
        // 更新遊戲計時器
        this.updateGameTimer();

        // 更新血量顯示
        this.updateHealthBars();

        // 更新技能冷卻顯示
        this.updateSkillCooldowns();
    }

    updateGameTimer() {
        if (!this.elements.gameTimer) return;

        const elapsed = Date.now() - this.gameStartTime;
        this.elements.gameTimer.textContent = Utils.formatTime(elapsed);
    }

    updateHealthBars() {
        if (this.scene.player1) {
            this.updatePlayerHealth(1, this.scene.player1);
        }

        if (this.scene.player2) {
            this.updatePlayerHealth(2, this.scene.player2);
        }
    }

    updatePlayerHealth(playerId, player) {
        const healthFill = this.elements[`player${playerId}Health`];
        const healthText = this.elements[`player${playerId}HealthText`];

        if (healthFill && healthText) {
            const healthPercent = (player.currentHP / player.maxHP) * 100;
            healthFill.style.width = `${healthPercent}%`;
            healthText.textContent = `${Math.ceil(player.currentHP)}/${player.maxHP}`;

            // 低血量警告效果
            if (healthPercent < 25) {
                healthFill.style.animation = 'pulse 0.5s infinite';
            } else {
                healthFill.style.animation = 'none';
            }
        }
    }

    updateSkillCooldowns() {
        if (!this.scene.player1 || !this.scene.player1.skillManager) return;

        const skillStatus = this.scene.player1.skillManager.getAllSkillStatus();

        this.elements.skillButtons.forEach(button => {
            const skillName = button.getAttribute('data-skill');
            const status = skillStatus[skillName];

            if (status) {
                this.updateSkillButton(button, status);
            }
        });
    }

    updateSkillButton(button, status) {
        const cooldownOverlay = button.querySelector('.cooldown-overlay');

        if (status.available) {
            button.classList.remove('cooldown');
            if (cooldownOverlay) {
                cooldownOverlay.style.height = '0%';
            }
        } else {
            button.classList.add('cooldown');
            if (cooldownOverlay && status.maxCooldown > 0) {
                const percentage = (status.cooldown / status.maxCooldown) * 100;
                cooldownOverlay.style.height = `${percentage}%`;
            }
        }
    }

    onPlayerDamaged(playerId, damage, source) {
        // 創建傷害數字顯示
        this.showDamageNumber(playerId, damage, '#ff0000');

        // 螢幕震動效果
        this.createScreenShake(damage * 0.5);

        // 血量條閃爍效果
        this.flashHealthBar(playerId, '#ff0000');
    }

    onPlayerHealed(playerId, amount) {
        // 創建治療數字顯示
        this.showDamageNumber(playerId, amount, '#00ff00', '+');

        // 血量條閃爍效果
        this.flashHealthBar(playerId, '#00ff00');
    }

    onModeChanged(playerId, newMode) {
        if (playerId === 1 && this.elements.modeIndicator) {
            // 更新模式指示器
            this.elements.modeIndicator.className = `mode-indicator ${newMode}`;

            const modeNames = {
                attack: '進攻模式',
                defense: '防守模式',
                neutral: '中性模式'
            };

            this.elements.modeIndicator.textContent = modeNames[newMode] || newMode;

            // 模式切換動畫
            this.elements.modeIndicator.style.transform = 'scale(1.2)';
            setTimeout(() => {
                this.elements.modeIndicator.style.transform = 'scale(1)';
            }, 200);
        }
    }

    onSkillUsed(playerId, skillName) {
        // 技能使用視覺反饋
        const button = document.querySelector(`[data-skill="${skillName}"]`);
        if (button && playerId === 1) {
            button.style.transform = 'scale(0.9)';
            setTimeout(() => {
                button.style.transform = 'scale(1)';
            }, 100);
        }

        // 顯示技能使用提示
        this.showSkillUsedIndicator(skillName);
    }

    onGameOver(winnerId) {
        // 顯示遊戲結束畫面
        this.showGameOverScreen(winnerId);
    }

    onSkillButtonClick(skillName) {
        if (this.scene.player1 && this.scene.player1.skillManager) {
            this.scene.player1.skillManager.useSkill(skillName);
        }
    }

    onKeyDown(event) {
        // 處理鍵盤快捷鍵
        if (this.scene.player1 && this.scene.player1.skillManager) {
            switch (event.code) {
                case 'KeyQ':
                    this.scene.player1.skillManager.useSkill('shockwave');
                    break;
                case 'KeyW':
                    this.scene.player1.skillManager.useSkill('shield');
                    break;
                case 'KeyE':
                    this.scene.player1.skillManager.useSkill('teleport');
                    break;
                case 'KeyR':
                    this.scene.player1.skillManager.useSkill('boost');
                    break;
            }
        }
    }

    showDamageNumber(playerId, amount, color, prefix = '-') {
        const player = playerId === 1 ? this.scene.player1 : this.scene.player2;
        if (!player) return;

        // 創建傷害數字元素
        const damageText = document.createElement('div');
        damageText.textContent = `${prefix}${Math.ceil(amount)}`;
        damageText.style.cssText = `
            position: absolute;
            color: ${color};
            font-weight: bold;
            font-size: 18px;
            pointer-events: none;
            z-index: 1001;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        `;

        // 計算螢幕位置
        const worldPos = player.body.position;
        const camera = this.scene.cameras.main;
        const screenPos = camera.getWorldPoint(worldPos.x, worldPos.y - 30);

        damageText.style.left = `${screenPos.x}px`;
        damageText.style.top = `${screenPos.y}px`;

        document.body.appendChild(damageText);

        // 動畫效果
        let startTime = Date.now();
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = elapsed / 1000; // 1秒動畫

            if (progress >= 1) {
                damageText.remove();
                return;
            }

            // 向上移動並淡出
            const y = screenPos.y - progress * 50;
            const opacity = 1 - progress;

            damageText.style.top = `${y}px`;
            damageText.style.opacity = opacity;

            requestAnimationFrame(animate);
        };

        requestAnimationFrame(animate);
    }

    flashHealthBar(playerId, color) {
        const healthBar = this.elements[`player${playerId}Health`];
        if (!healthBar) return;

        const originalFilter = healthBar.style.filter;
        healthBar.style.filter = `drop-shadow(0 0 10px ${color})`;

        setTimeout(() => {
            healthBar.style.filter = originalFilter;
        }, 200);
    }

    createScreenShake(intensity) {
        const gameContainer = document.getElementById('game-container');
        if (!gameContainer) return;

        const maxShake = Math.min(intensity, 10);
        const duration = 200;
        const startTime = Date.now();

        const shake = () => {
            const elapsed = Date.now() - startTime;
            const progress = elapsed / duration;

            if (progress >= 1) {
                gameContainer.style.transform = 'translate(0, 0)';
                return;
            }

            const currentIntensity = maxShake * (1 - progress);
            const x = (Math.random() - 0.5) * currentIntensity;
            const y = (Math.random() - 0.5) * currentIntensity;

            gameContainer.style.transform = `translate(${x}px, ${y}px)`;

            requestAnimationFrame(shake);
        };

        requestAnimationFrame(shake);
    }

    showSkillUsedIndicator(skillName) {
        // 在螢幕上顯示技能使用指示
        const indicator = document.createElement('div');
        indicator.textContent = skillName.toUpperCase();
        indicator.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 24px;
            font-weight: bold;
            pointer-events: none;
            z-index: 1002;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        `;

        document.body.appendChild(indicator);

        // 淡出動畫
        setTimeout(() => {
            indicator.style.transition = 'opacity 0.5s';
            indicator.style.opacity = '0';
            setTimeout(() => indicator.remove(), 500);
        }, 500);
    }

    showGameOverScreen(winnerId) {
        // 創建遊戲結束覆蓋層
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            color: white;
            font-family: Arial, sans-serif;
        `;

        const winnerColor = winnerId === 1 ? '#00d4ff' : '#ff6b35';
        const winnerText = `玩家 ${winnerId} 獲勝！`;

        overlay.innerHTML = `
            <h1 style="font-size: 48px; margin-bottom: 20px; color: ${winnerColor};">${winnerText}</h1>
            <p style="font-size: 18px; margin-bottom: 30px;">遊戲時間: ${Utils.formatTime(Date.now() - this.gameStartTime)}</p>
            <button onclick="location.reload()" style="
                padding: 15px 30px;
                font-size: 18px;
                background: ${winnerColor};
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
            ">重新開始</button>
        `;

        document.body.appendChild(overlay);
    }

    hideLoadingScreen() {
        if (this.elements.loadingScreen) {
            this.elements.loadingScreen.style.opacity = '0';
            setTimeout(() => {
                this.elements.loadingScreen.style.display = 'none';
            }, 500);
        }
    }

    showLoadingScreen() {
        if (this.elements.loadingScreen) {
            this.elements.loadingScreen.style.display = 'flex';
            this.elements.loadingScreen.style.opacity = '1';
        }
    }

    destroy() {
        // 清理事件監聽器
        this.scene.events.off(EVENTS.PLAYER_DAMAGED, this.onPlayerDamaged);
        this.scene.events.off(EVENTS.PLAYER_HEALED, this.onPlayerHealed);
        this.scene.events.off(EVENTS.MODE_CHANGED, this.onModeChanged);
        this.scene.events.off(EVENTS.SKILL_USED, this.onSkillUsed);
        this.scene.events.off(EVENTS.GAME_OVER, this.onGameOver);

        document.removeEventListener('keydown', this.onKeyDown);

        Utils.log('UIManager destroyed');
    }
}
