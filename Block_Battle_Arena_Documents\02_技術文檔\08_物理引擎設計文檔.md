# 物理引擎設計文檔
## Block Battle Arena - Physics Engine Design Document

---

## 📋 文檔資訊
- **版本號**: v1.0
- **創建日期**: 2025年6月3日
- **最後更新**: 2025年6月3日
- **關聯文檔**: 技術架構文檔、遊戲玩法設計文檔

---

## 🎯 物理引擎概述

### 設計理念
- **永不停歇**: 方塊在場地內永續運動，能量守恆
- **精確碰撞**: 精準的碰撞檢測，確保公平競技
- **可預測性**: 相同輸入產生相同結果，支援回放
- **性能優化**: 60FPS穩定運行，適配各種設備

### Matter.js 集成架構
```typescript
// 物理引擎管理器
class PhysicsManager {
  private engine: Matter.Engine;
  private world: Matter.World;
  private runner: Matter.Runner;
  
  constructor(config: PhysicsConfig) {
    this.initializeEngine(config);
    this.setupCollisionDetection();
    this.configureConstraints();
  }
  
  update(deltaTime: number): void {
    Matter.Engine.update(this.engine, deltaTime);
    this.handleCollisions();
    this.updatePlayerStates();
  }
}
```

---

## 🔮 物理世界設定

### 世界參數配置
```typescript
interface PhysicsConfig {
  // 重力設定
  gravity: {
    x: 0,
    y: 0.8,        // 輕微重力影響
    scale: 0.001   // 重力縮放
  };
  
  // 時間設定
  timing: {
    timeScale: 1.0,      // 時間縮放
    isFixed: true,       // 固定時間步長
    deltaTime: 1000/60   // 60FPS時間步長
  };
  
  // 求解器設定
  positionIterations: 6;  // 位置迭代次數
  velocityIterations: 4;  // 速度迭代次數
  constraintIterations: 2; // 約束迭代次數
  
  // 邊界設定
  enableSleeping: false;   // 禁用休眠
  broadphase: {
    type: 'grid',          // 使用網格廣相檢測
    bucketWidth: 48,
    bucketHeight: 48
  };
}
```

### 場地邊界系統
```typescript
class ArenaBoundaries {
  private walls: Matter.Body[] = [];
  private size: { width: number; height: number };
  
  constructor(width: number, height: number) {
    this.size = { width, height };
    this.createBoundaries();
  }
  
  private createBoundaries(): void {
    const thickness = 20;
    
    // 上邊界
    this.walls.push(Matter.Bodies.rectangle(
      this.size.width / 2, -thickness / 2,
      this.size.width, thickness,
      { isStatic: true, restitution: 1.0 }
    ));
    
    // 其他邊界...
  }
  
  addToWorld(world: Matter.World): void {
    Matter.World.add(world, this.walls);
  }
}
```

*本文檔最後更新: 2025年6月3日*
*版本: v1.0*