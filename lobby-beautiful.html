<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Block Battle Arena - 主大廳</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background:
                radial-gradient(circle at 20% 20%, rgba(255, 0, 150, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 255, 255, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(255, 255, 0, 0.2) 0%, transparent 50%),
                linear-gradient(135deg, #0a0a0f 0%, #1a0a2e 25%, #2e0a1a 50%, #0f0a3a 75%, #0a0a0f 100%);
            background-size: 400% 400%;
            animation: gameBackground 15s ease-in-out infinite;
            min-height: 100vh;
            color: white;
            overflow-x: hidden;
            position: relative;
        }

        @keyframes gameBackground {
            0%, 100% { background-position: 0% 50%; }
            25% { background-position: 100% 25%; }
            50% { background-position: 50% 100%; }
            75% { background-position: 25% 0%; }
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(0,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            pointer-events: none;
            z-index: -1;
            opacity: 0.3;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 頭部 */
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem 0;
        }

        .logo {
            font-size: 4.5rem;
            font-weight: 900;
            background: linear-gradient(45deg, #ff0080, #00ffff, #ffff00, #ff0080);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            animation: logoGlow 3s ease-in-out infinite;
            position: relative;
            display: inline-block;
        }

        @keyframes logoGlow {
            0%, 100% {
                background-position: 0% 50%;
                filter: drop-shadow(0 0 20px rgba(255, 0, 128, 0.8));
            }
            50% {
                background-position: 100% 50%;
                filter: drop-shadow(0 0 30px rgba(0, 255, 255, 0.8));
            }
        }

        .logo::before {
            content: '⚡';
            position: absolute;
            left: -60px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 3rem;
            animation: iconFloat 2s ease-in-out infinite;
        }

        .logo::after {
            content: '🎮';
            position: absolute;
            right: -60px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 3rem;
            animation: iconFloat 2s ease-in-out infinite reverse;
        }

        @keyframes iconFloat {
            0%, 100% { transform: translateY(-50%) rotate(0deg); }
            50% { transform: translateY(-60%) rotate(10deg); }
        }

        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 300;
        }

        /* 主要內容 */
        .main-grid {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 3rem;
            flex: 1;
        }

        /* 側邊欄 */
        .sidebar {
            background:
                linear-gradient(145deg, rgba(255, 0, 150, 0.1), rgba(0, 255, 255, 0.1)),
                rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 2rem;
            border: 2px solid transparent;
            background-clip: padding-box;
            height: fit-content;
            position: relative;
            overflow: hidden;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, #ff0080, #00ffff, #ffff00, #ff0080);
            z-index: -1;
            margin: -2px;
            border-radius: inherit;
            animation: borderGlow 4s ease-in-out infinite;
        }

        @keyframes borderGlow {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        .player-card {
            text-align: center;
            margin-bottom: 2rem;
        }

        .avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff0080, #00ffff, #ffff00);
            margin: 0 auto 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            font-weight: bold;
            box-shadow:
                0 0 30px rgba(255, 0, 128, 0.6),
                0 0 60px rgba(0, 255, 255, 0.4),
                inset 0 0 20px rgba(255, 255, 255, 0.2);
            position: relative;
            animation: avatarPulse 3s ease-in-out infinite;
            border: 4px solid rgba(255, 255, 255, 0.3);
        }

        @keyframes avatarPulse {
            0%, 100% {
                transform: scale(1);
                box-shadow:
                    0 0 30px rgba(255, 0, 128, 0.6),
                    0 0 60px rgba(0, 255, 255, 0.4);
            }
            50% {
                transform: scale(1.05);
                box-shadow:
                    0 0 40px rgba(255, 0, 128, 0.8),
                    0 0 80px rgba(0, 255, 255, 0.6);
            }
        }

        .avatar::before {
            content: '';
            position: absolute;
            top: -10px;
            right: -10px;
            width: 30px;
            height: 30px;
            background: linear-gradient(45deg, #ffff00, #ff0080);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            animation: badgeFloat 2s ease-in-out infinite;
            border: 2px solid white;
        }

        @keyframes badgeFloat {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-5px) rotate(10deg); }
        }

        .player-name {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #4ecdc4;
        }

        .stat-label {
            font-size: 0.8rem;
            opacity: 0.8;
            margin-top: 0.25rem;
        }

        /* 遊戲區域 */
        .game-area {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 2rem;
            text-align: center;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .game-modes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .mode-card {
            background:
                linear-gradient(145deg, rgba(255, 0, 150, 0.1), rgba(0, 255, 255, 0.1)),
                rgba(0, 0, 0, 0.4);
            border-radius: 20px;
            padding: 2.5rem 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid transparent;
            background-clip: padding-box;
            position: relative;
            overflow: hidden;
        }

        .mode-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, #ff0080, #00ffff, #ffff00, #ff0080);
            z-index: -1;
            margin: -2px;
            border-radius: inherit;
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        .mode-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s ease;
        }

        .mode-card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow:
                0 25px 50px rgba(255, 0, 128, 0.3),
                0 0 50px rgba(0, 255, 255, 0.2);
        }

        .mode-card:hover::before {
            opacity: 1;
        }

        .mode-card:hover::after {
            left: 100%;
        }

        .mode-icon {
            font-size: 5rem;
            margin-bottom: 1.5rem;
            display: block;
            filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.6));
            animation: iconBounce 3s ease-in-out infinite;
            position: relative;
        }

        @keyframes iconBounce {
            0%, 100% {
                transform: translateY(0) rotate(0deg) scale(1);
                filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.6));
            }
            50% {
                transform: translateY(-10px) rotate(5deg) scale(1.1);
                filter: drop-shadow(0 0 30px rgba(0, 255, 255, 0.8));
            }
        }

        .mode-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .mode-desc {
            opacity: 0.8;
            line-height: 1.5;
            margin-bottom: 1.5rem;
        }

        .mode-btn {
            background: linear-gradient(135deg, #ff0080, #00ffff);
            border: none;
            border-radius: 30px;
            padding: 1rem 2.5rem;
            color: white;
            font-weight: 700;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 8px 25px rgba(255, 0, 128, 0.4);
        }

        .mode-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s ease;
        }

        .mode-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 15px 35px rgba(255, 0, 128, 0.6);
            background: linear-gradient(135deg, #ff0080, #ffff00);
        }

        .mode-btn:hover::before {
            left: 100%;
        }

        /* 快速操作 */
        .quick-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .action-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 1rem 2rem;
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 150px;
        }

        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .action-btn.secondary {
            background: linear-gradient(135deg, #f093fb, #f5576c);
        }

        /* 響應式 */
        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .game-modes {
                grid-template-columns: 1fr;
            }

            .quick-actions {
                flex-direction: column;
                align-items: center;
            }

            .logo {
                font-size: 3rem;
            }
        }

        /* 動畫 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in {
            animation: fadeInUp 0.6s ease forwards;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 頭部 -->
        <header class="header fade-in">
            <h1 class="logo">Block Battle Arena</h1>
            <p class="subtitle">終極方塊競技場 - 準備好迎接挑戰了嗎？</p>
        </header>

        <!-- 主要內容 -->
        <div class="main-grid">
            <!-- 側邊欄 -->
            <aside class="sidebar fade-in">
                <div class="player-card">
                    <div class="avatar">P1</div>
                    <div class="player-name">玩家001</div>
                    <div class="stats">
                        <div class="stat">
                            <div class="stat-number">42</div>
                            <div class="stat-label">勝利</div>
                        </div>
                        <div class="stat">
                            <div class="stat-number">18</div>
                            <div class="stat-label">失敗</div>
                        </div>
                        <div class="stat">
                            <div class="stat-number">70%</div>
                            <div class="stat-label">勝率</div>
                        </div>
                        <div class="stat">
                            <div class="stat-number">156</div>
                            <div class="stat-label">排名</div>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 遊戲區域 -->
            <main class="game-area fade-in">
                <h2 class="section-title">選擇遊戲模式</h2>

                <div class="game-modes">
                    <div class="mode-card" onclick="startGame()">
                        <div class="mode-icon">⚡</div>
                        <div class="mode-title">快速對戰</div>
                        <div class="mode-desc">立即匹配對手，開始激烈的1v1戰鬥</div>
                        <button class="mode-btn">開始對戰</button>
                    </div>

                    <div class="mode-card" onclick="openRanked()">
                        <div class="mode-icon">🏆</div>
                        <div class="mode-title">排位賽</div>
                        <div class="mode-desc">參與排位賽，提升你的競技場排名</div>
                        <button class="mode-btn">進入排位</button>
                    </div>

                    <div class="mode-card" onclick="createRoom()">
                        <div class="mode-icon">🎮</div>
                        <div class="mode-title">自定義房間</div>
                        <div class="mode-desc">創建房間，邀請朋友一起對戰</div>
                        <button class="mode-btn">創建房間</button>
                    </div>

                    <div class="mode-card" onclick="openTraining()">
                        <div class="mode-icon">🎯</div>
                        <div class="mode-title">技能訓練</div>
                        <div class="mode-desc">練習技能組合，完善你的戰術</div>
                        <button class="mode-btn">開始訓練</button>
                    </div>
                </div>

                <div class="quick-actions">
                    <button class="action-btn" onclick="startGame()">立即開始</button>
                    <button class="action-btn secondary" onclick="openSettings()">設置</button>
                </div>
            </main>
        </div>
    </div>

    <script>
        function startGame() {
            window.location.href = 'index.html';
        }

        function openRanked() {
            alert('排位賽功能即將推出！');
        }

        function createRoom() {
            alert('自定義房間功能即將推出！');
        }

        function openTraining() {
            window.location.href = 'index.html';
        }

        function openSettings() {
            alert('設置功能即將推出！');
        }

        // 載入動畫
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.fade-in');
            elements.forEach((el, index) => {
                el.style.animationDelay = `${index * 0.2}s`;
            });
        });
    </script>
</body>
</html>
