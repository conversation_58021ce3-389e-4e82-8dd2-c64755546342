/**
 * 主要遊戲場景
 * Block Battle Arena - Game Scene
 */

class GameScene extends Phaser.Scene {
    constructor() {
        super({ key: 'GameScene' });

        this.player1 = null;
        this.player2 = null;
        this.walls = [];
        this.uiManager = null;
        this.gameState = GAME_STATES.PLAYING;
        this.lastUpdateTime = 0;

        // 技能配置
        this.playerSkills = ['SHOCKWAVE', 'SHIELD', 'TELEPORT', 'BOOST'];
        this.loadoutName = '新手友善';
    }

    init(data) {
        // 接收技能選擇數據
        if (data && data.playerSkills) {
            this.playerSkills = data.playerSkills;
            this.loadoutName = data.loadoutName || '自定義';
        }

        Utils.log(`GameScene initialized with loadout: ${this.loadoutName}`);
        Utils.log(`Player skills: ${this.playerSkills.join(', ')}`);
    }

    preload() {
        Utils.log('GameScene: Loading game assets...');

        // 設置物理引擎
        this.matter.world.setBounds(0, 0, GAME_CONFIG.SCREEN.WIDTH, GAME_CONFIG.SCREEN.HEIGHT);
        this.matter.world.disableGravity();
    }

    create() {
        Utils.log('GameScene: Creating game world...');

        // 創建背景
        this.createBackground();

        // 創建場地邊界
        this.createArena();

        // 創建玩家
        this.createPlayers();

        // 創建UI管理器
        this.uiManager = new UIManager(this);

        // 創建音效管理器
        this.audioManager = new AudioManager(this);

        // 設置碰撞檢測
        this.setupCollisions();

        // 設置攝影機
        this.setupCamera();

        // 隱藏載入畫面
        setTimeout(() => {
            this.uiManager.hideLoadingScreen();
        }, 1000);

        Utils.log('GameScene: Game world created successfully');
    }

    createBackground() {
        // 主背景
        this.add.rectangle(
            GAME_CONFIG.SCREEN.WIDTH / 2,
            GAME_CONFIG.SCREEN.HEIGHT / 2,
            GAME_CONFIG.SCREEN.WIDTH,
            GAME_CONFIG.SCREEN.HEIGHT,
            parseInt(GAME_CONFIG.SCREEN.BACKGROUND_COLOR.replace('#', '0x'))
        );

        // 添加網格背景
        this.createGridBackground();

        // 添加場地邊界視覺效果
        this.createArenaVisuals();
    }

    createGridBackground() {
        const graphics = this.add.graphics();
        graphics.lineStyle(1, 0x333333, 0.3);

        const gridSize = 50;

        // 垂直線
        for (let x = 0; x <= GAME_CONFIG.SCREEN.WIDTH; x += gridSize) {
            graphics.moveTo(x, 0);
            graphics.lineTo(x, GAME_CONFIG.SCREEN.HEIGHT);
        }

        // 水平線
        for (let y = 0; y <= GAME_CONFIG.SCREEN.HEIGHT; y += gridSize) {
            graphics.moveTo(0, y);
            graphics.lineTo(GAME_CONFIG.SCREEN.WIDTH, y);
        }

        graphics.strokePath();
    }

    createArenaVisuals() {
        const bounds = GAME_CONFIG.ARENA.BOUNDS;
        const thickness = GAME_CONFIG.ARENA.WALL_THICKNESS;

        // 場地邊界發光效果
        const graphics = this.add.graphics();
        graphics.lineStyle(4, 0x00d4ff, 0.8);
        graphics.strokeRect(
            bounds.LEFT - thickness/2,
            bounds.TOP - thickness/2,
            bounds.RIGHT - bounds.LEFT + thickness,
            bounds.BOTTOM - bounds.TOP + thickness
        );

        // 中線
        graphics.lineStyle(2, 0xffffff, 0.3);
        graphics.moveTo(GAME_CONFIG.SCREEN.WIDTH / 2, bounds.TOP);
        graphics.lineTo(GAME_CONFIG.SCREEN.WIDTH / 2, bounds.BOTTOM);
        graphics.strokePath();

        // 中心圓
        graphics.lineStyle(2, 0xffffff, 0.3);
        graphics.strokeCircle(
            GAME_CONFIG.SCREEN.WIDTH / 2,
            GAME_CONFIG.SCREEN.HEIGHT / 2,
            80
        );
    }

    createArena() {
        const bounds = GAME_CONFIG.ARENA.BOUNDS;
        const thickness = GAME_CONFIG.ARENA.WALL_THICKNESS;

        // 創建四面牆
        const walls = [
            // 上牆
            { x: GAME_CONFIG.SCREEN.WIDTH / 2, y: bounds.TOP - thickness/2, w: GAME_CONFIG.SCREEN.WIDTH, h: thickness },
            // 下牆
            { x: GAME_CONFIG.SCREEN.WIDTH / 2, y: bounds.BOTTOM + thickness/2, w: GAME_CONFIG.SCREEN.WIDTH, h: thickness },
            // 左牆
            { x: bounds.LEFT - thickness/2, y: GAME_CONFIG.SCREEN.HEIGHT / 2, w: thickness, h: GAME_CONFIG.SCREEN.HEIGHT },
            // 右牆
            { x: bounds.RIGHT + thickness/2, y: GAME_CONFIG.SCREEN.HEIGHT / 2, w: thickness, h: GAME_CONFIG.SCREEN.HEIGHT }
        ];

        walls.forEach(wall => {
            const wallBody = this.matter.add.rectangle(wall.x, wall.y, wall.w, wall.h, {
                isStatic: true,
                collisionFilter: {
                    category: COLLISION_CATEGORIES.WALL,
                    mask: COLLISION_CATEGORIES.PLAYER | COLLISION_CATEGORIES.PROJECTILE
                }
            });

            this.walls.push(wallBody);
        });

        Utils.log('Arena walls created');
    }

    createPlayers() {
        const spawn1 = GAME_CONFIG.PLAYER.SPAWN_POSITIONS.PLAYER1;
        const spawn2 = GAME_CONFIG.PLAYER.SPAWN_POSITIONS.PLAYER2;

        // 創建玩家1 (使用選擇的技能)
        this.player1 = new Player(this, spawn1.x, spawn1.y, 1, this.playerSkills);

        // 創建玩家2 (AI，使用隨機技能組合)
        const aiSkills = this.getRandomAISkills();
        this.player2 = new Player(this, spawn2.x, spawn2.y, 2, aiSkills);

        Utils.log('Players created');
        Utils.log(`Player 1 skills: ${this.playerSkills.join(', ')}`);
        Utils.log(`Player 2 (AI) skills: ${aiSkills.join(', ')}`);
    }

    getRandomAISkills() {
        // AI隨機選擇技能組合
        const loadouts = Object.keys(GAME_CONFIG.SKILL_LOADOUTS);
        const randomLoadout = Utils.randomChoice(loadouts);
        return GAME_CONFIG.SKILL_LOADOUTS[randomLoadout].SKILLS;
    }

    setupCollisions() {
        // 設置碰撞事件監聽
        this.matter.world.on('collisionstart', (event) => {
            this.handleCollisions(event);
        });

        Utils.log('Collision detection setup complete');
    }

    setupCamera() {
        // 設置攝影機跟隨
        this.cameras.main.setBounds(0, 0, GAME_CONFIG.SCREEN.WIDTH, GAME_CONFIG.SCREEN.HEIGHT);

        // 可選：攝影機跟隨兩個玩家的中點
        // this.cameras.main.startFollow(this.player1.sprite);
    }

    handleCollisions(event) {
        const pairs = event.pairs;

        for (let pair of pairs) {
            const bodyA = pair.bodyA;
            const bodyB = pair.bodyB;

            // 技能碰撞檢測
            this.handleSkillCollisions(bodyA, bodyB);

            // 玩家碰撞檢測
            this.handlePlayerCollisions(bodyA, bodyB);

            // 玩家與牆壁碰撞
            this.handleWallCollisions(bodyA, bodyB);
        }
    }

    handlePlayerCollisions(bodyA, bodyB) {
        // 檢查是否是玩家之間的碰撞
        let player1 = null;
        let player2 = null;

        if (bodyA.entityType === 'player' && bodyA.playerData instanceof Player) {
            player1 = bodyA.playerData;
        }
        if (bodyB.entityType === 'player' && bodyB.playerData instanceof Player) {
            player2 = bodyB.playerData;
        }

        // 如果兩個都是玩家，處理碰撞
        if (player1 && player2) {
            player1.handlePlayerCollision(player2);
        }
        // 如果只有一個是玩家，檢查與其他物體的碰撞
        else if (player1) {
            player1.handleCollisionWith(bodyB);
        }
        else if (player2) {
            player2.handleCollisionWith(bodyA);
        }
    }

    handleSkillCollisions(bodyA, bodyB) {
        let skill = null;
        let target = null;

        // 確定哪個是技能，哪個是目標
        if (bodyA.entityType === 'skill' && bodyA.skillData instanceof Skill) {
            skill = bodyA.skillData;
            target = bodyB.playerData || bodyB.skillData;
        } else if (bodyB.entityType === 'skill' && bodyB.skillData instanceof Skill) {
            skill = bodyB.skillData;
            target = bodyA.playerData || bodyA.skillData;
        }

        if (skill && target) {
            skill.onHit(target);
        }
    }

    handleWallCollisions(bodyA, bodyB) {
        // 檢查是否有物體撞到牆壁
        const isWallCollision = this.walls.includes(bodyA) || this.walls.includes(bodyB);

        if (isWallCollision) {
            // 可以在這裡添加牆壁碰撞的特殊效果
            Utils.log('Wall collision detected', 'collision');
        }
    }

    update(time, delta) {
        // 計算實際的 deltaTime (毫秒)
        const deltaTime = delta;

        // 更新遊戲狀態
        if (this.gameState === GAME_STATES.PLAYING) {
            // 更新玩家
            if (this.player1) {
                this.player1.update(deltaTime);
            }

            if (this.player2) {
                this.player2.update(deltaTime);
                this.updatePlayer2AI(deltaTime);
            }

            // 更新UI
            if (this.uiManager) {
                this.uiManager.update();
            }

            // 更新粒子效果
            this.updateParticleEffects(deltaTime);
        }

        this.lastUpdateTime = time;
    }

    updatePlayer2AI(deltaTime) {
        // 簡單的AI邏輯
        if (!this.player2 || !this.player1) return;

        const distance = Utils.distance(
            this.player1.body.position.x, this.player1.body.position.y,
            this.player2.body.position.x, this.player2.body.position.y
        );

        // AI決策邏輯
        if (Math.random() < 0.01) { // 1%機率每幀
            // 隨機切換模式
            if (Math.random() < 0.3) {
                this.player2.switchMode();
            }

            // 根據距離使用技能（使用AI的實際技能）
            if (this.player2.customSkills && this.player2.customSkills.length > 0) {
                const randomSkillIndex = Math.floor(Math.random() * this.player2.customSkills.length);
                const skillName = this.player2.customSkills[randomSkillIndex].toLowerCase();

                if (distance < 200 && Math.random() < 0.3) {
                    // 近距離使用第一個技能
                    this.player2.skillManager.useSkill(skillName,
                        this.player1.body.position.x,
                        this.player1.body.position.y
                    );
                } else if (this.player2.currentHP < 50 && Math.random() < 0.5) {
                    // 低血量使用防禦性技能
                    const defensiveSkills = this.player2.customSkills.filter(skill =>
                        ['SHIELD', 'REFLECTION_FIELD', 'GHOST_MODE'].includes(skill.toUpperCase())
                    );
                    if (defensiveSkills.length > 0) {
                        const defSkill = Utils.randomChoice(defensiveSkills).toLowerCase();
                        this.player2.skillManager.useSkill(defSkill);
                    }
                } else if (distance > 300 && Math.random() < 0.3) {
                    // 遠距離使用移動技能
                    const mobilitySkills = this.player2.customSkills.filter(skill =>
                        ['TELEPORT', 'BOOST', 'DASH_STRIKE'].includes(skill.toUpperCase())
                    );
                    if (mobilitySkills.length > 0) {
                        const mobSkill = Utils.randomChoice(mobilitySkills).toLowerCase();
                        this.player2.skillManager.useSkill(mobSkill,
                            this.player1.body.position.x + Utils.randomBetween(-100, 100),
                            this.player1.body.position.y + Utils.randomBetween(-100, 100)
                        );
                    }
                }
            }
        }
    }

    updateParticleEffects(deltaTime) {
        // 更新玩家粒子效果
        if (this.player1) {
            this.renderParticles(this.player1.particles);
        }

        if (this.player2) {
            this.renderParticles(this.player2.particles);
        }
    }

    renderParticles(particles) {
        // 簡單的粒子渲染 (使用 Phaser 的圖形系統)
        particles.forEach(particle => {
            if (particle.life > 0) {
                // 這裡可以創建臨時的視覺元素來表示粒子
                // 為了性能考慮，實際實現中可能需要使用對象池
            }
        });
    }

    // 遊戲事件處理
    onGameOver(winnerId) {
        this.gameState = GAME_STATES.GAME_OVER;
        Utils.log(`Game Over: Player ${winnerId} wins!`);

        // 停止所有玩家行動
        if (this.player1) {
            this.matter.body.setVelocity(this.player1.body, { x: 0, y: 0 });
        }
        if (this.player2) {
            this.matter.body.setVelocity(this.player2.body, { x: 0, y: 0 });
        }
    }

    // 暫停遊戲
    pauseGame() {
        this.gameState = GAME_STATES.PAUSED;
        this.matter.world.enabled = false;
    }

    // 恢復遊戲
    resumeGame() {
        this.gameState = GAME_STATES.PLAYING;
        this.matter.world.enabled = true;
    }

    // 重置遊戲
    resetGame() {
        this.scene.restart();
    }

    // 清理資源
    shutdown() {
        if (this.player1) {
            this.player1.destroy();
        }
        if (this.player2) {
            this.player2.destroy();
        }
        if (this.uiManager) {
            this.uiManager.destroy();
        }

        Utils.log('GameScene shutdown complete');
    }
}
