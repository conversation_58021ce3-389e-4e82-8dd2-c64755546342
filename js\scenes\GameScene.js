/**
 * 主要遊戲場景
 * Block Battle Arena - Game Scene
 */

class GameScene extends Phaser.Scene {
    constructor() {
        super({ key: 'GameScene' });

        this.player1 = null;
        this.player2 = null;
        this.walls = [];
        this.uiManager = null;
        this.gameState = GAME_STATES.PLAYING;
        this.lastUpdateTime = 0;

        // 技能配置
        this.playerSkills = ['SHOCKWAVE', 'SHIELD', 'TELEPORT', 'BOOST'];
        this.loadoutName = '新手友善';
    }

    init(data) {
        // 接收技能選擇數據
        if (data && data.playerSkills) {
            this.playerSkills = data.playerSkills;
            this.loadoutName = data.loadoutName || '自定義';
        }

        Utils.log(`GameScene initialized with loadout: ${this.loadoutName}`);
        Utils.log(`Player skills: ${this.playerSkills.join(', ')}`);
    }

    preload() {
        Utils.log('GameScene: Loading game assets...');

        // 設置物理引擎
        this.matter.world.setBounds(0, 0, GAME_CONFIG.SCREEN.WIDTH, GAME_CONFIG.SCREEN.HEIGHT);
        this.matter.world.disableGravity();
    }

    create() {
        Utils.log('GameScene: Creating game world...');

        // 創建背景
        this.createBackground();

        // 創建場地邊界
        this.createArena();

        // 創建玩家
        this.createPlayers();

        // 創建UI管理器
        this.uiManager = new UIManager(this);

        // 創建音效管理器
        this.audioManager = new AudioManager(this);

        // 設置碰撞檢測
        this.setupCollisions();

        // 設置攝影機
        this.setupCamera();

        // 顯示遊戲 UI 覆蓋層
        const uiOverlay = document.querySelector('#ui-overlay');
        if (uiOverlay) {
            uiOverlay.style.display = 'block';
        }

        // 隱藏載入畫面
        setTimeout(() => {
            this.uiManager.hideLoadingScreen();
        }, 1000);

        Utils.log('GameScene: Game world created successfully');
    }

    createBackground() {
        // 創建漸變背景
        this.createGradientBackground();

        // 添加粒子背景
        this.createParticleBackground();

        // 添加現代化場地
        this.createModernArena();
    }

    createGradientBackground() {
        // 使用 Graphics 創建漸變背景
        const graphics = this.add.graphics();

        // 創建徑向漸變效果
        const centerX = GAME_CONFIG.SCREEN.WIDTH / 2;
        const centerY = GAME_CONFIG.SCREEN.HEIGHT / 2;

        // 多層漸變圓圈
        const gradientLayers = [
            { radius: 400, color: 0x0a0a0f, alpha: 1 },
            { radius: 350, color: 0x1a1a2e, alpha: 0.8 },
            { radius: 300, color: 0x16213e, alpha: 0.6 },
            { radius: 250, color: 0x0f3460, alpha: 0.4 },
            { radius: 200, color: 0x00d4ff, alpha: 0.1 }
        ];

        gradientLayers.forEach(layer => {
            graphics.fillStyle(layer.color, layer.alpha);
            graphics.fillCircle(centerX, centerY, layer.radius);
        });
    }

    createParticleBackground() {
        // 創建浮動粒子背景
        this.backgroundParticles = [];

        for (let i = 0; i < 50; i++) {
            const particle = this.add.circle(
                Math.random() * GAME_CONFIG.SCREEN.WIDTH,
                Math.random() * GAME_CONFIG.SCREEN.HEIGHT,
                Math.random() * 3 + 1,
                0x00d4ff,
                Math.random() * 0.3 + 0.1
            );

            // 粒子浮動動畫
            this.tweens.add({
                targets: particle,
                x: particle.x + (Math.random() - 0.5) * 100,
                y: particle.y + (Math.random() - 0.5) * 100,
                alpha: Math.random() * 0.5 + 0.1,
                duration: Math.random() * 3000 + 2000,
                yoyo: true,
                repeat: -1,
                ease: 'Sine.easeInOut'
            });

            this.backgroundParticles.push(particle);
        }
    }

    createModernArena() {
        const bounds = GAME_CONFIG.ARENA.BOUNDS;

        // 創建現代化場地邊界
        this.createHolographicBorders(bounds);

        // 創建能量場效果
        this.createEnergyField(bounds);

        // 創建中央平台
        this.createCenterPlatform();
    }

    createHolographicBorders(bounds) {
        // 全息邊界效果
        const borderGraphics = this.add.graphics();

        // 主邊界 - 霓虹燈效果
        borderGraphics.lineStyle(4, 0x00ffff, 0.8);
        borderGraphics.strokeRect(bounds.LEFT, bounds.TOP, bounds.RIGHT - bounds.LEFT, bounds.BOTTOM - bounds.TOP);

        // 內邊界 - 柔和發光
        borderGraphics.lineStyle(2, 0xffffff, 0.4);
        borderGraphics.strokeRect(bounds.LEFT + 5, bounds.TOP + 5, bounds.RIGHT - bounds.LEFT - 10, bounds.BOTTOM - bounds.TOP - 10);

        // 外邊界 - 能量波動
        borderGraphics.lineStyle(8, 0x00ffff, 0.2);
        borderGraphics.strokeRect(bounds.LEFT - 5, bounds.TOP - 5, bounds.RIGHT - bounds.LEFT + 10, bounds.BOTTOM - bounds.TOP + 10);

        // 邊界動畫
        this.tweens.add({
            targets: borderGraphics,
            alpha: 0.6,
            duration: 1500,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
    }

    createEnergyField(bounds) {
        // 能量場網格
        const fieldGraphics = this.add.graphics();
        fieldGraphics.lineStyle(1, 0x00ffff, 0.1);

        const gridSize = 40;

        // 繪製能量網格
        for (let x = bounds.LEFT; x <= bounds.RIGHT; x += gridSize) {
            fieldGraphics.moveTo(x, bounds.TOP);
            fieldGraphics.lineTo(x, bounds.BOTTOM);
        }

        for (let y = bounds.TOP; y <= bounds.BOTTOM; y += gridSize) {
            fieldGraphics.moveTo(bounds.LEFT, y);
            fieldGraphics.lineTo(bounds.RIGHT, y);
        }

        fieldGraphics.strokePath();

        // 能量場脈衝
        this.tweens.add({
            targets: fieldGraphics,
            alpha: 0.05,
            duration: 2000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
    }

    createCenterPlatform() {
        const centerX = GAME_CONFIG.SCREEN.WIDTH / 2;
        const centerY = GAME_CONFIG.SCREEN.HEIGHT / 2;

        // 中央能量核心
        const core = this.add.circle(centerX, centerY, 30, 0x00ffff, 0.3);

        // 核心脈衝動畫
        this.tweens.add({
            targets: core,
            scaleX: 1.5,
            scaleY: 1.5,
            alpha: 0.1,
            duration: 2000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });

        // 環形能量圈
        const rings = [];
        for (let i = 1; i <= 3; i++) {
            const ring = this.add.circle(centerX, centerY, 50 + i * 20, 0x00ffff, 0);
            ring.setStrokeStyle(2, 0x00ffff, 0.3 - i * 0.1);
            rings.push(ring);

            // 環形旋轉動畫
            this.tweens.add({
                targets: ring,
                rotation: Math.PI * 2,
                duration: 5000 + i * 1000,
                repeat: -1,
                ease: 'Linear'
            });
        }
    }

    createGridBackground() {
        // 創建動態科技網格背景
        const graphics = this.add.graphics();

        // 主網格
        graphics.lineStyle(1, 0x00d4ff, 0.15);
        const gridSize = 50;

        // 垂直線
        for (let x = 0; x <= GAME_CONFIG.SCREEN.WIDTH; x += gridSize) {
            graphics.moveTo(x, 0);
            graphics.lineTo(x, GAME_CONFIG.SCREEN.HEIGHT);
        }

        // 水平線
        for (let y = 0; y <= GAME_CONFIG.SCREEN.HEIGHT; y += gridSize) {
            graphics.moveTo(0, y);
            graphics.lineTo(GAME_CONFIG.SCREEN.WIDTH, y);
        }

        graphics.strokePath();

        // 次級網格（更細密）
        const fineGraphics = this.add.graphics();
        fineGraphics.lineStyle(0.5, 0x00d4ff, 0.05);
        const fineGridSize = 25;

        for (let x = 0; x <= GAME_CONFIG.SCREEN.WIDTH; x += fineGridSize) {
            fineGraphics.moveTo(x, 0);
            fineGraphics.lineTo(x, GAME_CONFIG.SCREEN.HEIGHT);
        }

        for (let y = 0; y <= GAME_CONFIG.SCREEN.HEIGHT; y += fineGridSize) {
            fineGraphics.moveTo(0, y);
            fineGraphics.lineTo(GAME_CONFIG.SCREEN.WIDTH, y);
        }

        fineGraphics.strokePath();

        // 網格脈衝動畫
        this.tweens.add({
            targets: graphics,
            alpha: 0.05,
            duration: 3000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });

        this.tweens.add({
            targets: fineGraphics,
            alpha: 0.02,
            duration: 2000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
    }

    createArenaVisuals() {
        const bounds = GAME_CONFIG.ARENA.BOUNDS;
        const thickness = GAME_CONFIG.ARENA.WALL_THICKNESS;

        // 主邊界發光效果
        const mainBorder = this.add.graphics();
        mainBorder.lineStyle(6, 0x00d4ff, 0.8);
        mainBorder.strokeRect(
            bounds.LEFT - thickness/2,
            bounds.TOP - thickness/2,
            bounds.RIGHT - bounds.LEFT + thickness,
            bounds.BOTTOM - bounds.TOP + thickness
        );

        // 內邊界
        const innerBorder = this.add.graphics();
        innerBorder.lineStyle(2, 0xffffff, 0.4);
        innerBorder.strokeRect(
            bounds.LEFT - thickness/2 + 3,
            bounds.TOP - thickness/2 + 3,
            bounds.RIGHT - bounds.LEFT + thickness - 6,
            bounds.BOTTOM - bounds.TOP + thickness - 6
        );

        // 外發光邊界
        const outerGlow = this.add.graphics();
        outerGlow.lineStyle(10, 0x00d4ff, 0.2);
        outerGlow.strokeRect(
            bounds.LEFT - thickness/2 - 5,
            bounds.TOP - thickness/2 - 5,
            bounds.RIGHT - bounds.LEFT + thickness + 10,
            bounds.BOTTOM - bounds.TOP + thickness + 10
        );

        // 角落裝飾
        this.createCornerDecorations(bounds);

        // 中線（更炫酷）
        const centerLine = this.add.graphics();
        centerLine.lineStyle(3, 0xff6b35, 0.6);
        centerLine.moveTo(GAME_CONFIG.SCREEN.WIDTH / 2, bounds.TOP);
        centerLine.lineTo(GAME_CONFIG.SCREEN.WIDTH / 2, bounds.BOTTOM);
        centerLine.strokePath();

        // 中心圓（多層效果）
        const centerCircle = this.add.graphics();
        centerCircle.lineStyle(3, 0x00ff88, 0.6);
        centerCircle.strokeCircle(
            GAME_CONFIG.SCREEN.WIDTH / 2,
            GAME_CONFIG.SCREEN.HEIGHT / 2,
            80
        );

        centerCircle.lineStyle(1, 0xffffff, 0.3);
        centerCircle.strokeCircle(
            GAME_CONFIG.SCREEN.WIDTH / 2,
            GAME_CONFIG.SCREEN.HEIGHT / 2,
            85
        );

        // 邊界脈衝動畫
        this.tweens.add({
            targets: mainBorder,
            alpha: 0.6,
            duration: 2000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });

        this.tweens.add({
            targets: outerGlow,
            alpha: 0.1,
            duration: 1500,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });

        // 中心圓旋轉動畫
        this.tweens.add({
            targets: centerCircle,
            rotation: Math.PI * 2,
            duration: 10000,
            repeat: -1,
            ease: 'Linear'
        });
    }

    createCornerDecorations(bounds) {
        const cornerSize = 25;
        const corners = this.add.graphics();

        corners.lineStyle(4, 0xff6b35, 0.8);

        // 左上角
        corners.moveTo(bounds.LEFT - cornerSize, bounds.TOP);
        corners.lineTo(bounds.LEFT, bounds.TOP);
        corners.lineTo(bounds.LEFT, bounds.TOP - cornerSize);

        // 右上角
        corners.moveTo(bounds.RIGHT + cornerSize, bounds.TOP);
        corners.lineTo(bounds.RIGHT, bounds.TOP);
        corners.lineTo(bounds.RIGHT, bounds.TOP - cornerSize);

        // 左下角
        corners.moveTo(bounds.LEFT - cornerSize, bounds.BOTTOM);
        corners.lineTo(bounds.LEFT, bounds.BOTTOM);
        corners.lineTo(bounds.LEFT, bounds.BOTTOM + cornerSize);

        // 右下角
        corners.moveTo(bounds.RIGHT + cornerSize, bounds.BOTTOM);
        corners.lineTo(bounds.RIGHT, bounds.BOTTOM);
        corners.lineTo(bounds.RIGHT, bounds.BOTTOM + cornerSize);

        corners.strokePath();

        // 角落脈衝動畫
        this.tweens.add({
            targets: corners,
            scaleX: 1.1,
            scaleY: 1.1,
            duration: 1500,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
    }

    createArena() {
        const bounds = GAME_CONFIG.ARENA.BOUNDS;
        const thickness = GAME_CONFIG.ARENA.WALL_THICKNESS;

        // 創建四面牆
        const walls = [
            // 上牆
            { x: GAME_CONFIG.SCREEN.WIDTH / 2, y: bounds.TOP - thickness/2, w: GAME_CONFIG.SCREEN.WIDTH, h: thickness },
            // 下牆
            { x: GAME_CONFIG.SCREEN.WIDTH / 2, y: bounds.BOTTOM + thickness/2, w: GAME_CONFIG.SCREEN.WIDTH, h: thickness },
            // 左牆
            { x: bounds.LEFT - thickness/2, y: GAME_CONFIG.SCREEN.HEIGHT / 2, w: thickness, h: GAME_CONFIG.SCREEN.HEIGHT },
            // 右牆
            { x: bounds.RIGHT + thickness/2, y: GAME_CONFIG.SCREEN.HEIGHT / 2, w: thickness, h: GAME_CONFIG.SCREEN.HEIGHT }
        ];

        walls.forEach(wall => {
            const wallBody = this.matter.add.rectangle(wall.x, wall.y, wall.w, wall.h, {
                isStatic: true,
                collisionFilter: {
                    category: COLLISION_CATEGORIES.WALL,
                    mask: COLLISION_CATEGORIES.PLAYER | COLLISION_CATEGORIES.PROJECTILE
                }
            });

            this.walls.push(wallBody);
        });

        Utils.log('Arena walls created');
    }

    createPlayers() {
        const spawn1 = GAME_CONFIG.PLAYER.SPAWN_POSITIONS.PLAYER1;
        const spawn2 = GAME_CONFIG.PLAYER.SPAWN_POSITIONS.PLAYER2;

        // 創建玩家1 (使用選擇的技能)
        this.player1 = new Player(this, spawn1.x, spawn1.y, 1, this.playerSkills);

        // 創建玩家2 (AI，使用隨機技能組合)
        const aiSkills = this.getRandomAISkills();
        this.player2 = new Player(this, spawn2.x, spawn2.y, 2, aiSkills);

        Utils.log('Players created');
        Utils.log(`Player 1 skills: ${this.playerSkills.join(', ')}`);
        Utils.log(`Player 2 (AI) skills: ${aiSkills.join(', ')}`);
    }

    getRandomAISkills() {
        // AI隨機選擇技能組合
        const loadouts = Object.keys(GAME_CONFIG.SKILL_LOADOUTS);
        const randomLoadout = Utils.randomChoice(loadouts);
        return GAME_CONFIG.SKILL_LOADOUTS[randomLoadout].SKILLS;
    }

    setupCollisions() {
        // 設置碰撞事件監聽
        this.matter.world.on('collisionstart', (event) => {
            this.handleCollisions(event);
        });

        Utils.log('Collision detection setup complete');
    }

    setupCamera() {
        // 設置攝影機跟隨
        this.cameras.main.setBounds(0, 0, GAME_CONFIG.SCREEN.WIDTH, GAME_CONFIG.SCREEN.HEIGHT);

        // 可選：攝影機跟隨兩個玩家的中點
        // this.cameras.main.startFollow(this.player1.sprite);
    }

    handleCollisions(event) {
        const pairs = event.pairs;

        for (let pair of pairs) {
            const bodyA = pair.bodyA;
            const bodyB = pair.bodyB;

            // 技能碰撞檢測
            this.handleSkillCollisions(bodyA, bodyB);

            // 玩家碰撞檢測
            this.handlePlayerCollisions(bodyA, bodyB);

            // 玩家與牆壁碰撞
            this.handleWallCollisions(bodyA, bodyB);
        }
    }

    handlePlayerCollisions(bodyA, bodyB) {
        // 檢查是否是玩家之間的碰撞
        let player1 = null;
        let player2 = null;

        if (bodyA.entityType === 'player' && bodyA.playerData instanceof Player) {
            player1 = bodyA.playerData;
        }
        if (bodyB.entityType === 'player' && bodyB.playerData instanceof Player) {
            player2 = bodyB.playerData;
        }

        // 如果兩個都是玩家，處理碰撞
        if (player1 && player2) {
            player1.handlePlayerCollision(player2);
        }
        // 如果只有一個是玩家，檢查與其他物體的碰撞
        else if (player1) {
            player1.handleCollisionWith(bodyB);
        }
        else if (player2) {
            player2.handleCollisionWith(bodyA);
        }
    }

    handleSkillCollisions(bodyA, bodyB) {
        let skill = null;
        let target = null;

        // 確定哪個是技能，哪個是目標
        if (bodyA.entityType === 'skill' && bodyA.skillData instanceof Skill) {
            skill = bodyA.skillData;
            target = bodyB.playerData || bodyB.skillData;
        } else if (bodyB.entityType === 'skill' && bodyB.skillData instanceof Skill) {
            skill = bodyB.skillData;
            target = bodyA.playerData || bodyA.skillData;
        }

        if (skill && target) {
            skill.onHit(target);
        }
    }

    handleWallCollisions(bodyA, bodyB) {
        // 檢查是否有物體撞到牆壁
        const isWallCollision = this.walls.includes(bodyA) || this.walls.includes(bodyB);

        if (isWallCollision) {
            // 可以在這裡添加牆壁碰撞的特殊效果
            Utils.log('Wall collision detected', 'collision');
        }
    }

    update(time, delta) {
        // 計算實際的 deltaTime (毫秒)
        const deltaTime = delta;

        // 更新遊戲狀態
        if (this.gameState === GAME_STATES.PLAYING) {
            // 更新玩家
            if (this.player1) {
                this.player1.update(deltaTime);
            }

            if (this.player2) {
                this.player2.update(deltaTime);
                this.updatePlayer2AI(deltaTime);
            }

            // 更新UI
            if (this.uiManager) {
                this.uiManager.update();
            }

            // 更新粒子效果
            this.updateParticleEffects(deltaTime);
        }

        this.lastUpdateTime = time;
    }

    updatePlayer2AI(deltaTime) {
        // 簡單的AI邏輯
        if (!this.player2 || !this.player1) return;

        const distance = Utils.distance(
            this.player1.body.position.x, this.player1.body.position.y,
            this.player2.body.position.x, this.player2.body.position.y
        );

        // AI決策邏輯
        if (Math.random() < 0.01) { // 1%機率每幀
            // 隨機切換模式
            if (Math.random() < 0.3) {
                this.player2.switchMode();
            }

            // 根據距離使用技能（使用AI的實際技能）
            if (this.player2.customSkills && this.player2.customSkills.length > 0) {
                const randomSkillIndex = Math.floor(Math.random() * this.player2.customSkills.length);
                const skillName = this.player2.customSkills[randomSkillIndex].toLowerCase();

                if (distance < 200 && Math.random() < 0.3) {
                    // 近距離使用第一個技能
                    this.player2.skillManager.useSkill(skillName,
                        this.player1.body.position.x,
                        this.player1.body.position.y
                    );
                } else if (this.player2.currentHP < 50 && Math.random() < 0.5) {
                    // 低血量使用防禦性技能
                    const defensiveSkills = this.player2.customSkills.filter(skill =>
                        ['SHIELD', 'REFLECTION_FIELD', 'GHOST_MODE'].includes(skill.toUpperCase())
                    );
                    if (defensiveSkills.length > 0) {
                        const defSkill = Utils.randomChoice(defensiveSkills).toLowerCase();
                        this.player2.skillManager.useSkill(defSkill);
                    }
                } else if (distance > 300 && Math.random() < 0.3) {
                    // 遠距離使用移動技能
                    const mobilitySkills = this.player2.customSkills.filter(skill =>
                        ['TELEPORT', 'BOOST', 'DASH_STRIKE'].includes(skill.toUpperCase())
                    );
                    if (mobilitySkills.length > 0) {
                        const mobSkill = Utils.randomChoice(mobilitySkills).toLowerCase();
                        this.player2.skillManager.useSkill(mobSkill,
                            this.player1.body.position.x + Utils.randomBetween(-100, 100),
                            this.player1.body.position.y + Utils.randomBetween(-100, 100)
                        );
                    }
                }
            }
        }
    }

    updateParticleEffects(deltaTime) {
        // 更新玩家粒子效果
        if (this.player1) {
            this.renderParticles(this.player1.particles);
        }

        if (this.player2) {
            this.renderParticles(this.player2.particles);
        }
    }

    renderParticles(particles) {
        // 簡單的粒子渲染 (使用 Phaser 的圖形系統)
        particles.forEach(particle => {
            if (particle.life > 0) {
                // 這裡可以創建臨時的視覺元素來表示粒子
                // 為了性能考慮，實際實現中可能需要使用對象池
            }
        });
    }

    // 遊戲事件處理
    onGameOver(winnerId) {
        this.gameState = GAME_STATES.GAME_OVER;
        Utils.log(`Game Over: Player ${winnerId} wins!`);

        // 停止所有玩家行動
        if (this.player1) {
            this.matter.body.setVelocity(this.player1.body, { x: 0, y: 0 });
        }
        if (this.player2) {
            this.matter.body.setVelocity(this.player2.body, { x: 0, y: 0 });
        }
    }

    // 暫停遊戲
    pauseGame() {
        this.gameState = GAME_STATES.PAUSED;
        this.matter.world.enabled = false;
    }

    // 恢復遊戲
    resumeGame() {
        this.gameState = GAME_STATES.PLAYING;
        this.matter.world.enabled = true;
    }

    // 重置遊戲
    resetGame() {
        this.scene.restart();
    }

    // 清理資源
    shutdown() {
        if (this.player1) {
            this.player1.destroy();
        }
        if (this.player2) {
            this.player2.destroy();
        }
        if (this.uiManager) {
            this.uiManager.destroy();
        }

        Utils.log('GameScene shutdown complete');
    }
}
