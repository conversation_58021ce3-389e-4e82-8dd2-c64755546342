<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - Block Battle Arena</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a2e;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        #debug-info {
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        #game-container {
            border: 2px solid #00d4ff;
            border-radius: 10px;
            background: #000;
            width: 1000px;
            height: 700px;
        }
        
        .error {
            color: #ff6b35;
            background: rgba(255, 107, 53, 0.1);
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
        }
        
        .success {
            color: #00ff00;
            background: rgba(0, 255, 0, 0.1);
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
        }
        
        .warning {
            color: #ffdd00;
            background: rgba(255, 221, 0, 0.1);
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div id="debug-info">
        <h2>Debug Information</h2>
        <div id="debug-log"></div>
        <button onclick="testGame()">Test Game Initialization</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>
    
    <div id="game-container"></div>

    <!-- 載入腳本並檢查錯誤 -->
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/matter-js@0.19.0/build/matter.min.js"></script>
    
    <script>
        const debugLog = document.getElementById('debug-log');
        
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = type;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            debugLog.appendChild(div);
            console.log(message);
        }
        
        function clearLog() {
            debugLog.innerHTML = '';
        }
        
        // 檢查依賴
        log('Checking dependencies...', 'info');
        
        if (typeof Phaser !== 'undefined') {
            log('✓ Phaser.js loaded successfully', 'success');
        } else {
            log('✗ Phaser.js failed to load', 'error');
        }
        
        if (typeof Matter !== 'undefined') {
            log('✓ Matter.js loaded successfully', 'success');
        } else {
            log('✗ Matter.js failed to load', 'error');
        }
        
        // 載入遊戲腳本
        const scripts = [
            'js/config.js',
            'js/utils.js',
            'js/entities/Player.js',
            'js/entities/Skill.js',
            'js/entities/SkillExtensions.js',
            'js/managers/SkillManager.js',
            'js/managers/UIManager.js',
            'js/managers/AudioManager.js',
            'js/scenes/MenuScene.js',
            'js/scenes/SkillSelectScene.js',
            'js/scenes/GameScene.js',
            'js/scenes/GameOverScene.js'
        ];
        
        let loadedScripts = 0;
        
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = () => {
                    log(`✓ Loaded: ${src}`, 'success');
                    loadedScripts++;
                    resolve();
                };
                script.onerror = () => {
                    log(`✗ Failed to load: ${src}`, 'error');
                    reject(new Error(`Failed to load ${src}`));
                };
                document.head.appendChild(script);
            });
        }
        
        async function loadAllScripts() {
            try {
                for (const script of scripts) {
                    await loadScript(script);
                }
                log(`All scripts loaded successfully (${loadedScripts}/${scripts.length})`, 'success');
                
                // 檢查關鍵類是否定義
                checkClasses();
                
            } catch (error) {
                log(`Script loading failed: ${error.message}`, 'error');
            }
        }
        
        function checkClasses() {
            const classes = [
                'GAME_CONFIG',
                'Utils', 
                'Player',
                'Skill',
                'SkillManager',
                'UIManager',
                'AudioManager',
                'MenuScene',
                'SkillSelectScene',
                'GameScene',
                'GameOverScene'
            ];
            
            classes.forEach(className => {
                if (typeof window[className] !== 'undefined') {
                    log(`✓ Class defined: ${className}`, 'success');
                } else {
                    log(`✗ Class missing: ${className}`, 'error');
                }
            });
        }
        
        function testGame() {
            log('Testing game initialization...', 'info');
            
            try {
                if (typeof GAME_CONFIG === 'undefined') {
                    throw new Error('GAME_CONFIG is not defined');
                }
                
                const gameConfig = {
                    type: Phaser.AUTO,
                    width: 1000,
                    height: 700,
                    parent: 'game-container',
                    backgroundColor: '#1a1a2e',
                    physics: {
                        default: 'matter',
                        matter: {
                            gravity: { x: 0, y: 0 },
                            debug: false
                        }
                    },
                    scene: [MenuScene]
                };
                
                const game = new Phaser.Game(gameConfig);
                log('✓ Game initialized successfully!', 'success');
                
                // 隱藏載入畫面
                setTimeout(() => {
                    const loadingScreen = document.querySelector('#loading-screen');
                    if (loadingScreen) {
                        loadingScreen.style.display = 'none';
                    }
                }, 1000);
                
            } catch (error) {
                log(`✗ Game initialization failed: ${error.message}`, 'error');
                console.error(error);
            }
        }
        
        // 錯誤處理
        window.addEventListener('error', (event) => {
            log(`Global error: ${event.error.message}`, 'error');
        });
        
        // 開始載入
        loadAllScripts();
    </script>
</body>
</html>
