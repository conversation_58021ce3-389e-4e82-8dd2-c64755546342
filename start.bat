@echo off
echo ========================================
echo Block Battle Arena - 啟動腳本
echo ========================================
echo.

echo 正在檢查 Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 錯誤: 未找到 Node.js
    echo 請先安裝 Node.js: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js 已安裝
echo.

echo 正在安裝依賴...
npm install
if %errorlevel% neq 0 (
    echo ❌ 依賴安裝失敗
    pause
    exit /b 1
)

echo ✅ 依賴安裝完成
echo.

echo 正在啟動服務器...
echo 🚀 服務器將在 http://localhost:3000 啟動
echo 📱 遊戲首頁: http://localhost:3000
echo 🔐 登入頁面: http://localhost:3000/login
echo 📝 註冊頁面: http://localhost:3000/register
echo.
echo 按 Ctrl+C 停止服務器
echo.

npm start
