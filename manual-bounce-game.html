<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Block Battle Arena - 手動反彈版</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            font-family: 'Arial', sans-serif;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        #game-container {
            border: 2px solid #00d4ff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
            background: #000;
        }

        .loading-screen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 2000;
        }

        .loading-text {
            font-size: 24px;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div id="game-container">
        <div class="loading-screen" id="loading-screen">
            <div class="loading-text">載入中...</div>
            <div>Block Battle Arena - 手動反彈版</div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    
    <script>
        // 遊戲配置
        const GAME_CONFIG = {
            SCREEN: {
                WIDTH: 1000,
                HEIGHT: 700,
                BACKGROUND_COLOR: '#1a1a2e'
            },
            PLAYER: {
                SIZE: 30,
                MAX_HP: 100,
                BASE_SPEED: 300, // 像素/秒
                SPAWN_POSITIONS: {
                    PLAYER1: { x: 200, y: 350 },
                    PLAYER2: { x: 800, y: 350 }
                }
            },
            ARENA: {
                BOUNDS: {
                    LEFT: 50,
                    RIGHT: 950,
                    TOP: 50,
                    BOTTOM: 650
                }
            }
        };

        // 手動反彈遊戲場景
        class ManualBounceGameScene extends Phaser.Scene {
            constructor() {
                super({ key: 'ManualBounceGameScene' });
                this.players = [];
                this.bounds = GAME_CONFIG.ARENA.BOUNDS;
            }

            create() {
                console.log('ManualBounceGameScene: 開始創建...');
                
                // 背景
                this.add.rectangle(
                    GAME_CONFIG.SCREEN.WIDTH / 2,
                    GAME_CONFIG.SCREEN.HEIGHT / 2,
                    GAME_CONFIG.SCREEN.WIDTH,
                    GAME_CONFIG.SCREEN.HEIGHT,
                    0x1a1a2e
                );
                
                // 創建場地邊界
                this.createArena();
                
                // 創建玩家
                this.createPlayers();
                
                // 隱藏載入畫面
                setTimeout(() => {
                    const loadingScreen = document.querySelector('#loading-screen');
                    if (loadingScreen) {
                        loadingScreen.style.opacity = '0';
                        setTimeout(() => {
                            loadingScreen.style.display = 'none';
                        }, 500);
                    }
                }, 1000);
                
                console.log('ManualBounceGameScene: 創建完成');
            }

            createArena() {
                // 視覺邊界
                const graphics = this.add.graphics();
                graphics.lineStyle(4, 0x00d4ff, 0.8);
                graphics.strokeRect(
                    this.bounds.LEFT,
                    this.bounds.TOP,
                    this.bounds.RIGHT - this.bounds.LEFT,
                    this.bounds.BOTTOM - this.bounds.TOP
                );
                
                console.log('場地邊界創建完成');
            }

            createPlayers() {
                const spawn1 = GAME_CONFIG.PLAYER.SPAWN_POSITIONS.PLAYER1;
                const spawn2 = GAME_CONFIG.PLAYER.SPAWN_POSITIONS.PLAYER2;
                
                // 創建玩家1
                const player1 = {
                    sprite: this.add.rectangle(spawn1.x, spawn1.y, GAME_CONFIG.PLAYER.SIZE, GAME_CONFIG.PLAYER.SIZE, 0x00d4ff),
                    x: spawn1.x,
                    y: spawn1.y,
                    vx: 4,
                    vy: 3,
                    size: GAME_CONFIG.PLAYER.SIZE,
                    id: 1,
                    hp: GAME_CONFIG.PLAYER.MAX_HP
                };
                player1.sprite.setStrokeStyle(2, 0xffffff);
                
                // 創建玩家2
                const player2 = {
                    sprite: this.add.rectangle(spawn2.x, spawn2.y, GAME_CONFIG.PLAYER.SIZE, GAME_CONFIG.PLAYER.SIZE, 0xff6b35),
                    x: spawn2.x,
                    y: spawn2.y,
                    vx: -3,
                    vy: -4,
                    size: GAME_CONFIG.PLAYER.SIZE,
                    id: 2,
                    hp: GAME_CONFIG.PLAYER.MAX_HP
                };
                player2.sprite.setStrokeStyle(2, 0xffffff);
                
                this.players = [player1, player2];
                
                // 標題
                this.add.text(
                    GAME_CONFIG.SCREEN.WIDTH / 2,
                    100,
                    'Block Battle Arena - 手動反彈版',
                    {
                        fontSize: '24px',
                        fill: '#ffffff',
                        fontFamily: 'Arial'
                    }
                ).setOrigin(0.5);
                
                this.add.text(
                    GAME_CONFIG.SCREEN.WIDTH / 2,
                    130,
                    '完美反彈機制 - 永續運動',
                    {
                        fontSize: '16px',
                        fill: '#00ff00',
                        fontFamily: 'Arial'
                    }
                ).setOrigin(0.5);
                
                // 速度顯示
                this.speedText = this.add.text(
                    20, 20,
                    'Speed: P1(0,0) P2(0,0)',
                    {
                        fontSize: '14px',
                        fill: '#ffffff',
                        fontFamily: 'Arial'
                    }
                );
                
                // HP 顯示
                this.hpText = this.add.text(
                    20, 40,
                    'HP: P1(100) P2(100)',
                    {
                        fontSize: '14px',
                        fill: '#ffffff',
                        fontFamily: 'Arial'
                    }
                );
                
                console.log('玩家創建完成');
            }

            update(time, delta) {
                const deltaTime = delta / 1000; // 轉換為秒
                
                this.players.forEach((player, index) => {
                    // 更新位置
                    player.x += player.vx * deltaTime * 60; // 60fps 基準
                    player.y += player.vy * deltaTime * 60;
                    
                    // 檢查邊界碰撞並完美反彈
                    const halfSize = player.size / 2;
                    
                    // 左右邊界
                    if (player.x - halfSize <= this.bounds.LEFT) {
                        player.x = this.bounds.LEFT + halfSize;
                        player.vx = Math.abs(player.vx); // 確保向右
                        console.log(`玩家${player.id} 左邊界反彈`);
                    } else if (player.x + halfSize >= this.bounds.RIGHT) {
                        player.x = this.bounds.RIGHT - halfSize;
                        player.vx = -Math.abs(player.vx); // 確保向左
                        console.log(`玩家${player.id} 右邊界反彈`);
                    }
                    
                    // 上下邊界
                    if (player.y - halfSize <= this.bounds.TOP) {
                        player.y = this.bounds.TOP + halfSize;
                        player.vy = Math.abs(player.vy); // 確保向下
                        console.log(`玩家${player.id} 上邊界反彈`);
                    } else if (player.y + halfSize >= this.bounds.BOTTOM) {
                        player.y = this.bounds.BOTTOM - halfSize;
                        player.vy = -Math.abs(player.vy); // 確保向上
                        console.log(`玩家${player.id} 下邊界反彈`);
                    }
                    
                    // 檢查玩家間碰撞
                    if (index === 0) {
                        this.checkPlayerCollision();
                    }
                    
                    // 更新視覺位置
                    player.sprite.setPosition(player.x, player.y);
                });
                
                // 更新UI
                this.updateUI();
            }

            checkPlayerCollision() {
                const player1 = this.players[0];
                const player2 = this.players[1];
                
                const dx = player1.x - player2.x;
                const dy = player1.y - player2.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                const minDistance = (player1.size + player2.size) / 2;
                
                if (distance < minDistance) {
                    console.log('玩家碰撞檢測');
                    
                    // 分離玩家
                    const overlap = minDistance - distance;
                    const separationX = (dx / distance) * overlap * 0.5;
                    const separationY = (dy / distance) * overlap * 0.5;
                    
                    player1.x += separationX;
                    player1.y += separationY;
                    player2.x -= separationX;
                    player2.y -= separationY;
                    
                    // 交換速度（彈性碰撞）
                    const tempVx = player1.vx;
                    const tempVy = player1.vy;
                    player1.vx = player2.vx;
                    player1.vy = player2.vy;
                    player2.vx = tempVx;
                    player2.vy = tempVy;
                    
                    // 造成傷害
                    const damage = 10;
                    player1.hp = Math.max(0, player1.hp - damage);
                    player2.hp = Math.max(0, player2.hp - damage);
                    
                    console.log(`玩家碰撞 - 雙方受到 ${damage} 傷害`);
                    
                    // 檢查遊戲結束
                    if (player1.hp <= 0 || player2.hp <= 0) {
                        this.endGame();
                    }
                }
            }

            updateUI() {
                // 更新速度顯示
                const p1Speed = `(${this.players[0].vx.toFixed(1)},${this.players[0].vy.toFixed(1)})`;
                const p2Speed = `(${this.players[1].vx.toFixed(1)},${this.players[1].vy.toFixed(1)})`;
                this.speedText.setText(`Speed: P1${p1Speed} P2${p2Speed}`);
                
                // 更新HP顯示
                this.hpText.setText(`HP: P1(${this.players[0].hp}) P2(${this.players[1].hp})`);
            }

            endGame() {
                const winner = this.players[0].hp > this.players[1].hp ? 'Player 1' : 'Player 2';
                
                this.add.text(
                    GAME_CONFIG.SCREEN.WIDTH / 2,
                    GAME_CONFIG.SCREEN.HEIGHT / 2,
                    `${winner} 獲勝！`,
                    {
                        fontSize: '48px',
                        fill: '#ffff00',
                        fontFamily: 'Arial',
                        stroke: '#000000',
                        strokeThickness: 4
                    }
                ).setOrigin(0.5);
                
                // 停止遊戲
                this.scene.pause();
                
                console.log(`遊戲結束 - ${winner} 獲勝`);
            }
        }

        // 遊戲配置
        const gameConfig = {
            type: Phaser.AUTO,
            width: GAME_CONFIG.SCREEN.WIDTH,
            height: GAME_CONFIG.SCREEN.HEIGHT,
            parent: 'game-container',
            backgroundColor: GAME_CONFIG.SCREEN.BACKGROUND_COLOR,
            scene: [ManualBounceGameScene]
        };

        // 錯誤處理
        window.addEventListener('error', (event) => {
            const errorMessage = event.error ? event.error.message : 'Unknown error';
            console.error('Error:', errorMessage);
            
            const loadingScreen = document.querySelector('#loading-screen');
            if (loadingScreen) {
                loadingScreen.innerHTML = `
                    <div style="color: #ff6b35; font-size: 24px; margin-bottom: 20px;">載入失敗</div>
                    <div style="color: #cccccc; font-size: 16px; text-align: center;">
                        錯誤: ${errorMessage}<br><br>
                        <button onclick="location.reload()" style="
                            padding: 10px 20px;
                            background: #00d4ff;
                            color: white;
                            border: none;
                            border-radius: 5px;
                            cursor: pointer;
                        ">重新載入</button>
                    </div>
                `;
            }
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('初始化手動反彈遊戲...');
            
            try {
                const game = new Phaser.Game(gameConfig);
                console.log('手動反彈遊戲初始化成功');
            } catch (error) {
                console.error('遊戲初始化失敗:', error);
            }
        });
    </script>
</body>
</html>
