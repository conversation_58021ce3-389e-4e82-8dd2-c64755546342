<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Bounce Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a2e;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        #game-container {
            border: 2px solid #00d4ff;
            border-radius: 10px;
            background: #000;
            width: 800px;
            height: 600px;
            margin: 20px auto;
        }
        
        .info {
            text-align: center;
            margin: 20px 0;
        }
        
        .debug {
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="info">
        <h1>簡單反彈測試</h1>
        <p>使用最基本的 Phaser + Matter.js 設置</p>
    </div>
    
    <div id="game-container"></div>
    
    <div class="info">
        <button onclick="addBall()">添加球</button>
        <button onclick="resetTest()">重置</button>
        <button onclick="toggleDebug()">調試模式</button>
    </div>
    
    <div class="debug" id="debug-info">
        載入中...
    </div>

    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/matter-js@0.19.0/build/matter.min.js"></script>
    
    <script>
        let game;
        let scene;
        let debugInfo = document.getElementById('debug-info');
        
        function log(message) {
            console.log(message);
            debugInfo.innerHTML += message + '<br>';
            debugInfo.scrollTop = debugInfo.scrollHeight;
        }
        
        class SimpleBounceScene extends Phaser.Scene {
            constructor() {
                super({ key: 'SimpleBounceScene' });
                this.balls = [];
                this.frameCount = 0;
            }

            create() {
                log('SimpleBounceScene: 開始創建...');
                
                // 設置物理世界
                this.matter.world.disableGravity();
                log('重力已禁用');
                
                // 創建邊界牆壁 - 使用最簡單的方法
                this.createWalls();
                
                // 創建一個測試球
                this.createTestBall();
                
                // 設置碰撞監聽
                this.matter.world.on('collisionstart', (event) => {
                    log(`碰撞檢測: ${event.pairs.length} 對碰撞`);
                });
                
                // 添加文字
                this.add.text(400, 50, '簡單反彈測試', {
                    fontSize: '24px',
                    fill: '#ffffff',
                    fontFamily: 'Arial'
                }).setOrigin(0.5);
                
                this.statusText = this.add.text(20, 20, '狀態: 初始化', {
                    fontSize: '16px',
                    fill: '#ffffff',
                    fontFamily: 'Arial'
                });
                
                log('場景創建完成');
            }

            createWalls() {
                log('創建邊界牆壁...');
                
                const wallThickness = 50;
                const wallOptions = {
                    isStatic: true,
                    restitution: 1.0,
                    friction: 0,
                    frictionStatic: 0,
                    frictionAir: 0
                };
                
                // 上牆
                this.topWall = this.matter.add.rectangle(400, wallThickness/2, 800, wallThickness, wallOptions);
                this.add.rectangle(400, wallThickness/2, 800, wallThickness, 0x666666);
                
                // 下牆
                this.bottomWall = this.matter.add.rectangle(400, 600 - wallThickness/2, 800, wallThickness, wallOptions);
                this.add.rectangle(400, 600 - wallThickness/2, 800, wallThickness, 0x666666);
                
                // 左牆
                this.leftWall = this.matter.add.rectangle(wallThickness/2, 300, wallThickness, 600, wallOptions);
                this.add.rectangle(wallThickness/2, 300, wallThickness, 600, 0x666666);
                
                // 右牆
                this.rightWall = this.matter.add.rectangle(800 - wallThickness/2, 300, wallThickness, 600, wallOptions);
                this.add.rectangle(800 - wallThickness/2, 300, wallThickness, 600, 0x666666);
                
                log('4面牆壁創建完成');
            }

            createTestBall() {
                log('創建測試球...');
                
                const x = 400;
                const y = 300;
                const radius = 20;
                
                // 創建物理球體
                this.testBall = this.matter.add.circle(x, y, radius, {
                    restitution: 1.0,     // 完美彈性
                    friction: 0,          // 無摩擦
                    frictionAir: 0,       // 無空氣阻力
                    density: 1,           // 標準密度
                    inertia: Infinity     // 防止旋轉
                });
                
                // 創建視覺球體
                this.testBallSprite = this.add.circle(x, y, radius, 0x00d4ff);
                this.testBallSprite.setStrokeStyle(3, 0xffffff);
                
                // 設置初始速度
                const initialVelocity = { x: 5, y: 3 };
                this.matter.body.setVelocity(this.testBall, initialVelocity);
                
                this.balls.push({
                    body: this.testBall,
                    sprite: this.testBallSprite
                });
                
                log(`測試球創建完成，初始速度: (${initialVelocity.x}, ${initialVelocity.y})`);
            }

            addBall() {
                const x = 200 + Math.random() * 400;
                const y = 150 + Math.random() * 300;
                const radius = 15 + Math.random() * 10;
                const color = Math.random() * 0xffffff;
                
                // 創建物理球體
                const ballBody = this.matter.add.circle(x, y, radius, {
                    restitution: 1.0,
                    friction: 0,
                    frictionAir: 0,
                    density: 1,
                    inertia: Infinity
                });
                
                // 創建視覺球體
                const ballSprite = this.add.circle(x, y, radius, color);
                ballSprite.setStrokeStyle(2, 0xffffff);
                
                // 隨機初始速度
                const vx = (Math.random() - 0.5) * 10;
                const vy = (Math.random() - 0.5) * 10;
                this.matter.body.setVelocity(ballBody, { x: vx, y: vy });
                
                this.balls.push({
                    body: ballBody,
                    sprite: ballSprite
                });
                
                log(`新球添加: 位置(${x.toFixed(1)}, ${y.toFixed(1)}) 速度(${vx.toFixed(1)}, ${vy.toFixed(1)})`);
            }

            update() {
                this.frameCount++;
                
                // 更新所有球的視覺位置
                this.balls.forEach((ball, index) => {
                    if (ball.body && ball.sprite) {
                        ball.sprite.setPosition(ball.body.position.x, ball.body.position.y);
                        
                        // 檢查速度
                        const speed = Math.sqrt(ball.body.velocity.x ** 2 + ball.body.velocity.y ** 2);
                        
                        // 如果速度太低，重新設置
                        if (speed < 0.5) {
                            const angle = Math.random() * Math.PI * 2;
                            const newSpeed = 3 + Math.random() * 3;
                            this.matter.body.setVelocity(ball.body, {
                                x: Math.cos(angle) * newSpeed,
                                y: Math.sin(angle) * newSpeed
                            });
                            log(`球 ${index} 速度重置: 新速度 ${newSpeed.toFixed(1)}`);
                        }
                    }
                });
                
                // 每60幀更新一次狀態
                if (this.frameCount % 60 === 0) {
                    this.updateStatus();
                }
            }

            updateStatus() {
                if (this.balls.length > 0) {
                    const ball = this.balls[0];
                    if (ball.body) {
                        const pos = ball.body.position;
                        const vel = ball.body.velocity;
                        const speed = Math.sqrt(vel.x ** 2 + vel.y ** 2);
                        
                        this.statusText.setText(
                            `位置: (${pos.x.toFixed(1)}, ${pos.y.toFixed(1)}) ` +
                            `速度: (${vel.x.toFixed(1)}, ${vel.y.toFixed(1)}) ` +
                            `速率: ${speed.toFixed(1)}`
                        );
                    }
                }
            }

            resetTest() {
                log('重置測試...');
                
                // 清除所有球
                this.balls.forEach(ball => {
                    if (ball.body) {
                        this.matter.world.remove(ball.body);
                    }
                    if (ball.sprite) {
                        ball.sprite.destroy();
                    }
                });
                this.balls = [];
                
                // 重新創建測試球
                this.createTestBall();
                
                log('測試重置完成');
            }
        }

        // 遊戲配置
        const gameConfig = {
            type: Phaser.AUTO,
            width: 800,
            height: 600,
            parent: 'game-container',
            backgroundColor: '#1a1a2e',
            scene: [SimpleBounceScene],
            physics: {
                default: 'matter',
                matter: {
                    gravity: { x: 0, y: 0 },
                    debug: false,
                    enableSleeping: false,  // 禁用睡眠模式
                    debugBodyColor: 0x00ff00,
                    debugBodyFillColor: 0x00ff00,
                    debugStaticBodyColor: 0xff0000,
                    debugVelocityColor: 0x00ffff
                }
            }
        };

        // 全域函數
        window.addBall = function() {
            if (scene) {
                scene.addBall();
            }
        };

        window.resetTest = function() {
            if (scene) {
                scene.resetTest();
            }
        };

        window.toggleDebug = function() {
            if (game && game.scene.scenes[0]) {
                const currentScene = game.scene.scenes[0];
                if (currentScene.matter) {
                    const debug = currentScene.matter.world.debugGraphic;
                    if (debug) {
                        debug.visible = !debug.visible;
                        log(`調試模式: ${debug.visible ? '開啟' : '關閉'}`);
                    } else {
                        currentScene.matter.world.createDebugGraphic();
                        log('調試模式: 開啟');
                    }
                }
            }
        };

        // 錯誤處理
        window.addEventListener('error', (event) => {
            log(`錯誤: ${event.error ? event.error.message : 'Unknown error'}`);
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('開始初始化簡單反彈測試...');
            
            try {
                game = new Phaser.Game(gameConfig);
                scene = game.scene.scenes[0];
                log('簡單反彈測試初始化成功');
            } catch (error) {
                log(`初始化失敗: ${error.message}`);
            }
        });
    </script>
</body>
</html>
