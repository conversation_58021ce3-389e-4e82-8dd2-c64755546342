<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Block Battle Arena - Basic Version</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            font-family: 'Arial', sans-serif;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        #game-container {
            border: 2px solid #00d4ff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
            background: #000;
        }

        .loading-screen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 2000;
        }

        .loading-text {
            font-size: 24px;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div id="game-container">
        <div class="loading-screen" id="loading-screen">
            <div class="loading-text">載入中...</div>
            <div>Block Battle Arena - 基本版</div>
        </div>
    </div>

    <!-- 遊戲腳本 -->
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/matter-js@0.19.0/build/matter.min.js"></script>
    
    <!-- 只載入基本的腳本 -->
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/entities/Player.js"></script>
    <script src="js/entities/Skill.js"></script>
    <script src="js/managers/SkillManager.js"></script>
    <script src="js/managers/UIManager.js"></script>
    <script src="js/scenes/MenuScene.js"></script>
    <script src="js/scenes/GameScene.js"></script>
    <script src="js/scenes/GameOverScene.js"></script>
    
    <script>
        // 基本遊戲配置（不包含擴展技能）
        const basicGameConfig = {
            type: Phaser.AUTO,
            width: GAME_CONFIG.SCREEN.WIDTH,
            height: GAME_CONFIG.SCREEN.HEIGHT,
            parent: 'game-container',
            backgroundColor: GAME_CONFIG.SCREEN.BACKGROUND_COLOR,
            
            // 物理引擎配置
            physics: {
                default: 'matter',
                matter: {
                    gravity: GAME_CONFIG.PHYSICS.GRAVITY,
                    debug: false
                }
            },
            
            // 場景配置（只包含基本場景）
            scene: [MenuScene, GameScene, GameOverScene],
            
            // 輸入配置
            input: {
                keyboard: true,
                mouse: true,
                touch: true
            },
            
            // 渲染配置
            render: {
                antialias: true,
                pixelArt: false,
                roundPixels: false
            }
        };

        // 錯誤處理
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            
            // 顯示錯誤信息
            const loadingScreen = document.querySelector('#loading-screen');
            if (loadingScreen) {
                loadingScreen.innerHTML = `
                    <div style="color: #ff6b35; font-size: 24px; margin-bottom: 20px;">載入失敗</div>
                    <div style="color: #cccccc; font-size: 16px; text-align: center;">
                        錯誤信息: ${event.error.message}<br><br>
                        <button onclick="location.reload()" style="
                            padding: 10px 20px;
                            background: #00d4ff;
                            color: white;
                            border: none;
                            border-radius: 5px;
                            cursor: pointer;
                        ">重新載入</button>
                    </div>
                `;
            }
        });

        // 等待所有腳本載入完成
        document.addEventListener('DOMContentLoaded', () => {
            console.log('DOM loaded, checking dependencies...');
            
            // 檢查必要的類是否已定義
            const requiredClasses = [
                'GAME_CONFIG', 'Utils', 'Player', 'Skill', 
                'SkillManager', 'UIManager', 'MenuScene', 
                'GameScene', 'GameOverScene'
            ];
            
            let allLoaded = true;
            requiredClasses.forEach(className => {
                if (typeof window[className] === 'undefined') {
                    console.error(`Missing class: ${className}`);
                    allLoaded = false;
                }
            });
            
            if (!allLoaded) {
                const loadingScreen = document.querySelector('#loading-screen');
                if (loadingScreen) {
                    loadingScreen.innerHTML = `
                        <div style="color: #ff6b35; font-size: 24px; margin-bottom: 20px;">載入失敗</div>
                        <div style="color: #cccccc; font-size: 16px; text-align: center;">
                            某些必要的類沒有正確載入<br><br>
                            <button onclick="location.reload()" style="
                                padding: 10px 20px;
                                background: #00d4ff;
                                color: white;
                                border: none;
                                border-radius: 5px;
                                cursor: pointer;
                            ">重新載入</button>
                        </div>
                    `;
                }
                return;
            }
            
            console.log('All dependencies loaded, initializing game...');
            
            try {
                // 初始化遊戲
                const game = new Phaser.Game(basicGameConfig);
                console.log('Basic game initialized successfully');
                
                // 隱藏載入畫面
                setTimeout(() => {
                    const loadingScreen = document.querySelector('#loading-screen');
                    if (loadingScreen) {
                        loadingScreen.style.opacity = '0';
                        setTimeout(() => {
                            loadingScreen.style.display = 'none';
                        }, 500);
                    }
                }, 1000);
                
            } catch (error) {
                console.error('Game initialization failed:', error);
                
                const loadingScreen = document.querySelector('#loading-screen');
                if (loadingScreen) {
                    loadingScreen.innerHTML = `
                        <div style="color: #ff6b35; font-size: 24px; margin-bottom: 20px;">初始化失敗</div>
                        <div style="color: #cccccc; font-size: 16px; text-align: center;">
                            ${error.message}<br><br>
                            <button onclick="location.reload()" style="
                                padding: 10px 20px;
                                background: #00d4ff;
                                color: white;
                                border: none;
                                border-radius: 5px;
                                cursor: pointer;
                            ">重新載入</button>
                        </div>
                    `;
                }
            }
        });
    </script>
</body>
</html>
