<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Physics Test - 物理測試</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a2e;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        #game-container {
            border: 2px solid #00d4ff;
            border-radius: 10px;
            background: #000;
            width: 800px;
            height: 600px;
            margin: 20px auto;
        }
        
        .info {
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="info">
        <h1>物理引擎測試</h1>
        <p>測試 Matter.js 的彈性碰撞是否正常工作</p>
    </div>
    
    <div id="game-container"></div>
    
    <div class="info">
        <p>如果看到方塊在邊界反彈，表示物理引擎工作正常</p>
        <button onclick="addBall()">添加球</button>
        <button onclick="toggleDebug()">切換調試模式</button>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/matter-js@0.19.0/build/matter.min.js"></script>
    
    <script>
        let game;
        let scene;
        
        class PhysicsTestScene extends Phaser.Scene {
            constructor() {
                super({ key: 'PhysicsTestScene' });
                this.balls = [];
            }

            preload() {
                console.log('PhysicsTestScene: Preloading...');
            }

            create() {
                console.log('PhysicsTestScene: Creating...');
                
                // 設置物理世界
                this.matter.world.setBounds(0, 0, 800, 600, 32, true, true, false, true);
                this.matter.world.disableGravity();
                
                // 創建邊界牆壁
                this.createWalls();
                
                // 創建測試球
                this.createTestBalls();
                
                // 設置碰撞檢測
                this.matter.world.on('collisionstart', (event) => {
                    console.log('Collision detected:', event.pairs.length, 'pairs');
                });
                
                // 添加說明文字
                this.add.text(400, 50, '物理引擎測試', {
                    fontSize: '24px',
                    fill: '#ffffff',
                    fontFamily: 'Arial'
                }).setOrigin(0.5);
                
                this.add.text(400, 80, '觀察方塊是否在邊界反彈', {
                    fontSize: '16px',
                    fill: '#cccccc',
                    fontFamily: 'Arial'
                }).setOrigin(0.5);
                
                // 速度顯示
                this.speedText = this.add.text(20, 20, 'Speeds:', {
                    fontSize: '14px',
                    fill: '#ffffff',
                    fontFamily: 'Arial'
                });
            }

            createWalls() {
                // 創建四面牆壁
                const wallThickness = 32;
                
                // 上牆
                const topWall = this.matter.add.rectangle(400, wallThickness/2, 800, wallThickness, {
                    isStatic: true,
                    restitution: 1.0,
                    friction: 0,
                    frictionStatic: 0
                });
                
                // 下牆
                const bottomWall = this.matter.add.rectangle(400, 600 - wallThickness/2, 800, wallThickness, {
                    isStatic: true,
                    restitution: 1.0,
                    friction: 0,
                    frictionStatic: 0
                });
                
                // 左牆
                const leftWall = this.matter.add.rectangle(wallThickness/2, 300, wallThickness, 600, {
                    isStatic: true,
                    restitution: 1.0,
                    friction: 0,
                    frictionStatic: 0
                });
                
                // 右牆
                const rightWall = this.matter.add.rectangle(800 - wallThickness/2, 300, wallThickness, 600, {
                    isStatic: true,
                    restitution: 1.0,
                    friction: 0,
                    frictionStatic: 0
                });
                
                // 視覺化牆壁
                this.add.rectangle(400, wallThickness/2, 800, wallThickness, 0x666666);
                this.add.rectangle(400, 600 - wallThickness/2, 800, wallThickness, 0x666666);
                this.add.rectangle(wallThickness/2, 300, wallThickness, 600, 0x666666);
                this.add.rectangle(800 - wallThickness/2, 300, wallThickness, 600, 0x666666);
                
                console.log('Walls created');
            }

            createTestBalls() {
                // 創建幾個測試球
                this.addBall(200, 200, 0x00d4ff, 4, 3);
                this.addBall(600, 400, 0xff6b35, -3, -4);
                this.addBall(400, 300, 0x00ff00, 5, -2);
            }

            addBall(x = null, y = null, color = null, vx = null, vy = null) {
                // 隨機位置和顏色（如果沒有指定）
                x = x || 100 + Math.random() * 600;
                y = y || 100 + Math.random() * 400;
                color = color || Math.random() * 0xffffff;
                vx = vx || (Math.random() - 0.5) * 10;
                vy = vy || (Math.random() - 0.5) * 10;
                
                // 創建物理實體
                const ballBody = this.matter.add.circle(x, y, 15, {
                    restitution: 1.0,
                    friction: 0,
                    frictionAir: 0,
                    density: 1,
                    inertia: Infinity // 防止旋轉
                });
                
                // 創建視覺實體
                const ballSprite = this.add.circle(x, y, 15, color);
                ballSprite.setStrokeStyle(2, 0xffffff);
                
                // 設置初始速度
                this.matter.body.setVelocity(ballBody, { x: vx, y: vy });
                
                // 保存引用
                const ball = { body: ballBody, sprite: ballSprite, id: this.balls.length };
                this.balls.push(ball);
                
                console.log(`Ball ${ball.id} created at (${x}, ${y}) with velocity (${vx}, ${vy})`);
                
                return ball;
            }

            update() {
                // 更新所有球的視覺位置
                this.balls.forEach(ball => {
                    if (ball.body && ball.sprite) {
                        ball.sprite.setPosition(ball.body.position.x, ball.body.position.y);
                    }
                });
                
                // 更新速度顯示
                if (this.speedText && this.balls.length > 0) {
                    let speedInfo = 'Speeds:\n';
                    this.balls.forEach((ball, index) => {
                        if (ball.body) {
                            const vx = ball.body.velocity.x.toFixed(1);
                            const vy = ball.body.velocity.y.toFixed(1);
                            const speed = Math.sqrt(ball.body.velocity.x ** 2 + ball.body.velocity.y ** 2).toFixed(1);
                            speedInfo += `Ball ${index}: (${vx}, ${vy}) |${speed}|\n`;
                        }
                    });
                    this.speedText.setText(speedInfo);
                }
            }
        }

        // 遊戲配置
        const gameConfig = {
            type: Phaser.AUTO,
            width: 800,
            height: 600,
            parent: 'game-container',
            backgroundColor: '#1a1a2e',
            scene: [PhysicsTestScene],
            physics: {
                default: 'matter',
                matter: {
                    gravity: { x: 0, y: 0 },
                    debug: false,
                    debugBodyColor: 0x00ff00,
                    debugBodyFillColor: 0x00ff00,
                    debugStaticBodyColor: 0xff0000,
                    debugVelocityColor: 0x00ffff
                }
            }
        };

        // 全域函數
        window.addBall = function() {
            if (scene) {
                scene.addBall();
            }
        };

        window.toggleDebug = function() {
            if (game && game.scene.scenes[0]) {
                const currentScene = game.scene.scenes[0];
                if (currentScene.matter) {
                    const debug = currentScene.matter.world.debugGraphic;
                    if (debug) {
                        debug.visible = !debug.visible;
                    } else {
                        currentScene.matter.world.createDebugGraphic();
                    }
                }
            }
        };

        // 錯誤處理
        window.addEventListener('error', (event) => {
            console.error('Error:', event.error);
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Initializing physics test...');
            
            try {
                game = new Phaser.Game(gameConfig);
                scene = game.scene.scenes[0];
                console.log('Physics test initialized successfully');
            } catch (error) {
                console.error('Initialization failed:', error);
            }
        });
    </script>
</body>
</html>
