<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Block Battle Arena - 註冊</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: 
                linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6)),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1920 1080"><defs><linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%23667eea;stop-opacity:1" /><stop offset="50%" style="stop-color:%23764ba2;stop-opacity:1" /><stop offset="100%" style="stop-color:%23f093fb;stop-opacity:1" /></linearGradient></defs><rect width="1920" height="1080" fill="url(%23bg)"/><circle cx="300" cy="200" r="100" fill="rgba(255,255,255,0.1)"/><circle cx="1600" cy="300" r="150" fill="rgba(255,255,255,0.05)"/><circle cx="800" cy="800" r="200" fill="rgba(255,255,255,0.08)"/></svg>');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            min-height: 100vh;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .register-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .logo {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            color: white;
        }

        .subtitle {
            opacity: 0.8;
            margin-bottom: 2rem;
            font-size: 0.9rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
            text-align: left;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: white;
        }

        .form-input {
            width: 100%;
            padding: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            background: rgba(255, 255, 255, 0.15);
        }

        .register-btn {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }

        .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .register-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .divider {
            margin: 1.5rem 0;
            position: relative;
            text-align: center;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: rgba(255, 255, 255, 0.2);
        }

        .divider span {
            background: rgba(255, 255, 255, 0.1);
            padding: 0 1rem;
            font-size: 0.9rem;
            opacity: 0.7;
        }

        .login-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .login-link:hover {
            color: #f093fb;
        }

        .back-btn {
            position: absolute;
            top: 2rem;
            left: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }

        .error-message {
            background: rgba(255, 0, 0, 0.2);
            border: 1px solid rgba(255, 0, 0, 0.3);
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 1rem;
            color: #ff6b6b;
            font-size: 0.9rem;
            display: none;
        }

        .success-message {
            background: rgba(0, 255, 0, 0.2);
            border: 1px solid rgba(0, 255, 0, 0.3);
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 1rem;
            color: #51cf66;
            font-size: 0.9rem;
            display: none;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 響應式 */
        @media (max-width: 768px) {
            .register-container {
                margin: 1rem;
                padding: 2rem;
            }
            
            .back-btn {
                top: 1rem;
                left: 1rem;
            }
        }

        /* 載入動畫 */
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 0.8s ease forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <button class="back-btn" onclick="goBack()">←</button>
    
    <div class="register-container fade-in">
        <h1 class="logo">Block Battle Arena</h1>
        <p class="subtitle">創建新帳號加入戰鬥</p>
        
        <div class="error-message" id="errorMessage"></div>
        <div class="success-message" id="successMessage"></div>
        
        <form id="registerForm">
            <div class="form-group">
                <label class="form-label" for="username">用戶名稱</label>
                <input 
                    type="text" 
                    id="username" 
                    class="form-input" 
                    placeholder="請輸入用戶名稱"
                    required
                >
            </div>
            
            <div class="form-group">
                <label class="form-label" for="email">電子郵件</label>
                <input 
                    type="email" 
                    id="email" 
                    class="form-input" 
                    placeholder="請輸入電子郵件"
                    required
                >
            </div>
            
            <div class="form-group">
                <label class="form-label" for="password">密碼</label>
                <input 
                    type="password" 
                    id="password" 
                    class="form-input" 
                    placeholder="請輸入密碼（至少6位）"
                    required
                >
            </div>
            
            <div class="form-group">
                <label class="form-label" for="confirmPassword">確認密碼</label>
                <input 
                    type="password" 
                    id="confirmPassword" 
                    class="form-input" 
                    placeholder="請再次輸入密碼"
                    required
                >
            </div>
            
            <button type="submit" class="register-btn" id="registerBtn">
                創建帳號
            </button>
        </form>
        
        <div class="divider">
            <span>已經有帳號？</span>
        </div>
        
        <a href="login.html" class="login-link">立即登入</a>
    </div>

    <script>
        function goBack() {
            window.location.href = 'home.html';
        }

        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            const successDiv = document.getElementById('successMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            successDiv.style.display = 'none';
        }

        function showSuccess(message) {
            const errorDiv = document.getElementById('errorMessage');
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            errorDiv.style.display = 'none';
        }

        function hideMessages() {
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
        }

        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const registerBtn = document.getElementById('registerBtn');
            
            // 驗證輸入
            if (!username || !email || !password || !confirmPassword) {
                showError('請填寫所有欄位');
                return;
            }
            
            if (password.length < 6) {
                showError('密碼至少需要6位字符');
                return;
            }
            
            if (password !== confirmPassword) {
                showError('密碼確認不一致');
                return;
            }
            
            // 顯示載入狀態
            registerBtn.disabled = true;
            registerBtn.innerHTML = '<span class="loading"></span> 創建中...';
            hideMessages();
            
            try {
                const response = await fetch('/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, email, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showSuccess('註冊成功！正在跳轉到登入頁面...');
                    
                    // 跳轉到登入頁面
                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 2000);
                } else {
                    showError(data.message || '註冊失敗，請稍後再試');
                }
            } catch (error) {
                console.error('註冊錯誤:', error);
                showError('網路錯誤，請稍後再試');
            } finally {
                registerBtn.disabled = false;
                registerBtn.innerHTML = '創建帳號';
            }
        });

        // 檢查是否已經登入
        if (localStorage.getItem('userToken')) {
            window.location.href = 'lobby.html';
        }
    </script>
</body>
</html>
