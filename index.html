<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Block Battle Arena - 方塊競技場</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;700&display=swap');

        body {
            margin: 0;
            padding: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
                linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #0a0a0f 100%);
            font-family: 'Exo 2', 'Arial', sans-serif;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            animation: backgroundShift 20s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% { filter: hue-rotate(0deg); }
            50% { filter: hue-rotate(30deg); }
        }

        #game-container {
            border: 3px solid transparent;
            border-radius: 20px;
            background:
                linear-gradient(45deg, #000, #111) padding-box,
                linear-gradient(45deg, #00d4ff, #ff6b35, #00ff88, #ff3366) border-box;
            box-shadow:
                0 0 50px rgba(0, 212, 255, 0.4),
                inset 0 0 50px rgba(0, 0, 0, 0.5),
                0 0 100px rgba(255, 107, 53, 0.2);
            position: relative;
            overflow: hidden;
        }

        #game-container::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #00d4ff, #ff6b35, #00ff88, #ff3366);
            border-radius: 20px;
            z-index: -1;
            animation: borderGlow 3s ease-in-out infinite;
        }

        @keyframes borderGlow {
            0%, 100% { opacity: 0.8; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.02); }
        }

        #ui-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1000;
            display: none; /* 默認隱藏，只在遊戲中顯示 */
        }

        .health-bar {
            position: absolute;
            top: 20px;
            width: 250px;
            height: 25px;
            background:
                linear-gradient(90deg, rgba(0, 0, 0, 0.8) 0%, rgba(20, 20, 40, 0.8) 100%);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            overflow: hidden;
            backdrop-filter: blur(10px);
            box-shadow:
                0 4px 15px rgba(0, 0, 0, 0.3),
                inset 0 2px 5px rgba(255, 255, 255, 0.1);
        }

        .health-bar.player1 {
            left: 20px;
        }

        .health-bar.player2 {
            right: 20px;
        }

        .health-fill {
            height: 100%;
            transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .health-fill.player1 {
            background:
                linear-gradient(90deg, #00d4ff 0%, #0099cc 50%, #00ffff 100%);
            box-shadow:
                0 0 20px rgba(0, 212, 255, 0.6),
                inset 0 2px 10px rgba(255, 255, 255, 0.3);
        }

        .health-fill.player2 {
            background:
                linear-gradient(90deg, #ff6b35 0%, #cc5529 50%, #ff8c42 100%);
            box-shadow:
                0 0 20px rgba(255, 107, 53, 0.6),
                inset 0 2px 10px rgba(255, 255, 255, 0.3);
        }

        .health-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: healthShine 2s infinite;
        }

        @keyframes healthShine {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .health-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            font-size: 12px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        }

        .skill-bar {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
            pointer-events: auto;
        }

        .skill-button {
            width: 60px;
            height: 60px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            background:
                linear-gradient(145deg, rgba(0, 0, 0, 0.8) 0%, rgba(30, 30, 60, 0.8) 100%);
            color: white;
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            font-size: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            box-shadow:
                0 4px 15px rgba(0, 0, 0, 0.3),
                inset 0 2px 5px rgba(255, 255, 255, 0.1);
        }

        .skill-button:hover {
            background:
                linear-gradient(145deg, rgba(0, 212, 255, 0.2) 0%, rgba(30, 30, 60, 0.9) 100%);
            border-color: rgba(0, 212, 255, 0.8);
            transform: translateY(-2px) scale(1.05);
            box-shadow:
                0 8px 25px rgba(0, 212, 255, 0.3),
                inset 0 2px 10px rgba(255, 255, 255, 0.2);
        }

        .skill-button.cooldown {
            opacity: 0.6;
            cursor: not-allowed;
            filter: grayscale(0.7);
        }

        .skill-button .cooldown-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            background:
                linear-gradient(180deg, rgba(255, 0, 0, 0.8) 0%, rgba(150, 0, 0, 0.9) 100%);
            transition: height 0.1s linear;
            border-radius: 0 0 10px 10px;
        }

        .skill-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .skill-button:hover::before {
            left: 100%;
        }

        .mode-indicator {
            position: absolute;
            top: 60px;
            left: 20px;
            padding: 10px 15px;
            border-radius: 20px;
            color: white;
            font-weight: bold;
            text-transform: uppercase;
            border: 2px solid;
            background: rgba(0, 0, 0, 0.7);
        }

        .mode-indicator.attack {
            border-color: #ff6b35;
            color: #ff6b35;
        }

        .mode-indicator.defense {
            border-color: #00d4ff;
            color: #00d4ff;
        }

        .mode-indicator.neutral {
            border-color: #ffffff;
            color: #ffffff;
        }

        .game-timer {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }

        .loading-screen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at center, rgba(0, 212, 255, 0.1) 0%, transparent 70%),
                linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #0a0a0f 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 2000;
            backdrop-filter: blur(5px);
        }

        .loading-text {
            font-family: 'Orbitron', monospace;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 30px;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.8);
            animation: loadingPulse 2s ease-in-out infinite;
        }

        .loading-subtitle {
            font-family: 'Exo 2', sans-serif;
            font-size: 16px;
            font-weight: 300;
            opacity: 0.8;
            margin-bottom: 40px;
            animation: loadingFade 3s ease-in-out infinite;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 3px solid rgba(255, 255, 255, 0.1);
            border-top: 3px solid #00d4ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes loadingPulse {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
                text-shadow: 0 0 20px rgba(0, 212, 255, 0.8);
            }
            50% {
                opacity: 0.7;
                transform: scale(1.05);
                text-shadow: 0 0 30px rgba(0, 212, 255, 1);
            }
        }

        @keyframes loadingFade {
            0%, 100% { opacity: 0.8; }
            50% { opacity: 0.4; }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .controls-info {
            position: absolute;
            bottom: 20px;
            right: 20px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
            text-align: right;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div id="game-container">
        <div id="ui-overlay">
            <!-- 血量條 -->
            <div class="health-bar player1">
                <div class="health-fill player1" style="width: 100%;"></div>
                <div class="health-text">100/100</div>
            </div>
            <div class="health-bar player2">
                <div class="health-fill player2" style="width: 100%;"></div>
                <div class="health-text">100/100</div>
            </div>

            <!-- 遊戲計時器 -->
            <div class="game-timer">00:00</div>

            <!-- 模式指示器 -->
            <div class="mode-indicator neutral">中性模式</div>

            <!-- 技能欄 -->
            <div class="skill-bar">
                <div class="skill-button" data-skill="shockwave" title="衝擊波 (Q)">
                    Q
                    <div class="cooldown-overlay" style="height: 0%;"></div>
                </div>
                <div class="skill-button" data-skill="shield" title="能量護盾 (W)">
                    W
                    <div class="cooldown-overlay" style="height: 0%;"></div>
                </div>
                <div class="skill-button" data-skill="teleport" title="瞬移 (E)">
                    E
                    <div class="cooldown-overlay" style="height: 0%;"></div>
                </div>
                <div class="skill-button" data-skill="boost" title="瞬間加速 (R)">
                    R
                    <div class="cooldown-overlay" style="height: 0%;"></div>
                </div>
            </div>

            <!-- 控制說明 -->
            <div class="controls-info">
                <div><strong>控制說明:</strong></div>
                <div>空白鍵: 切換模式</div>
                <div>Q/W/E/R: 技能</div>
                <div>滑鼠: 瞄準方向</div>
            </div>
        </div>

        <!-- 載入畫面 -->
        <div class="loading-screen" id="loading-screen">
            <div class="loading-text">BLOCK BATTLE ARENA</div>
            <div class="loading-subtitle">方塊競技場 - 升級版</div>
            <div class="loading-spinner"></div>
            <div class="loading-subtitle">載入中...</div>
        </div>
    </div>

    <!-- 遊戲腳本 -->
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/matter-js@0.19.0/build/matter.min.js"></script>

    <!-- 錯誤處理 -->
    <script>
        window.addEventListener('error', (event) => {
            const errorMessage = event.error ? event.error.message : 'Unknown error';
            console.error('Script loading error:', errorMessage);

            const loadingScreen = document.querySelector('#loading-screen');
            if (loadingScreen) {
                loadingScreen.innerHTML = `
                    <div style="color: #ff6b35; font-size: 24px; margin-bottom: 20px;">載入失敗</div>
                    <div style="color: #cccccc; font-size: 16px; text-align: center;">
                        錯誤: ${errorMessage}<br><br>
                        <button onclick="location.href='fixed.html'" style="
                            padding: 10px 20px;
                            background: #00d4ff;
                            color: white;
                            border: none;
                            border-radius: 5px;
                            cursor: pointer;
                            margin: 5px;
                        ">載入修復版</button>
                        <button onclick="location.reload()" style="
                            padding: 10px 20px;
                            background: #ff6b35;
                            color: white;
                            border: none;
                            border-radius: 5px;
                            cursor: pointer;
                            margin: 5px;
                        ">重新載入</button>
                    </div>
                `;
            }
        });
    </script>

    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/entities/Player.js"></script>
    <script src="js/entities/Skill.js"></script>
    <script src="js/entities/SkillExtensions.js"></script>
    <script src="js/managers/SkillManager.js"></script>
    <script src="js/managers/UIManager.js"></script>
    <script src="js/managers/AudioManager.js"></script>
    <script src="js/scenes/MenuScene.js"></script>
    <script src="js/scenes/SkillSelectScene.js"></script>
    <script src="js/scenes/GameScene.js"></script>
    <script src="js/scenes/GameOverScene.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
