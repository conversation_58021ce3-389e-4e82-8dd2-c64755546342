<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Block Battle Arena - 主大廳</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background:
                conic-gradient(from 0deg at 50% 50%, #ff0080, #00ffff, #ffff00, #ff0080, #8000ff, #00ff80, #ff0080),
                radial-gradient(circle at 30% 70%, rgba(255, 0, 128, 0.4) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(0, 255, 255, 0.4) 0%, transparent 50%),
                linear-gradient(45deg, #000000 0%, #1a0033 50%, #000000 100%);
            background-size: 400% 400%, 100% 100%, 100% 100%, 100% 100%;
            animation: psychedelicBackground 8s ease-in-out infinite;
            min-height: 100vh;
            color: white;
            overflow: hidden;
            position: relative;
        }

        @keyframes psychedelicBackground {
            0%, 100% { background-position: 0% 50%, 0% 0%, 0% 0%, 0% 0%; }
            25% { background-position: 100% 25%, 0% 0%, 0% 0%, 0% 0%; }
            50% { background-position: 50% 100%, 0% 0%, 0% 0%, 0% 0%; }
            75% { background-position: 25% 0%, 0% 0%, 0% 0%, 0% 0%; }
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hexagon" width="20" height="17.32" patternUnits="userSpaceOnUse"><polygon points="10,1 18.66,6 18.66,16 10,21 1.34,16 1.34,6" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23hexagon)"/></svg>');
            pointer-events: none;
            z-index: -1;
            opacity: 0.3;
            animation: hexagonFloat 6s ease-in-out infinite;
        }

        @keyframes hexagonFloat {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(2deg); }
        }

        .container {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding: 2rem;
        }

        .hexagon-layout {
            position: relative;
            width: 800px;
            height: 600px;
            transform: rotate(0deg);
            animation: hexagonRotate 20s linear infinite;
        }

        @keyframes hexagonRotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hex-item {
            position: absolute;
            width: 200px;
            height: 200px;
            clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            transform-origin: center;
            animation: hexItemFloat 4s ease-in-out infinite;
        }

        @keyframes hexItemFloat {
            0%, 100% { transform: translateY(0) scale(1); }
            50% { transform: translateY(-10px) scale(1.05); }
        }

        .hex-item:hover {
            transform: scale(1.2) rotate(-20deg);
            z-index: 10;
        }

        /* 六邊形位置 */
        .hex-center {
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 250px;
            height: 250px;
            background: conic-gradient(from 0deg, #ff0080, #00ffff, #ffff00, #ff0080);
            animation: centerPulse 3s ease-in-out infinite;
        }

        @keyframes centerPulse {
            0%, 100% { transform: translate(-50%, -50%) scale(1); }
            50% { transform: translate(-50%, -50%) scale(1.1); }
        }

        .hex-1 {
            top: 10%;
            left: 50%;
            transform: translate(-50%, 0);
            background: linear-gradient(135deg, #ff0080, #8000ff);
        }

        .hex-2 {
            top: 30%;
            right: 10%;
            background: linear-gradient(135deg, #00ffff, #0080ff);
        }

        .hex-3 {
            bottom: 30%;
            right: 10%;
            background: linear-gradient(135deg, #ffff00, #ff8000);
        }

        .hex-4 {
            bottom: 10%;
            left: 50%;
            transform: translate(-50%, 0);
            background: linear-gradient(135deg, #00ff80, #0080ff);
        }

        .hex-5 {
            bottom: 30%;
            left: 10%;
            background: linear-gradient(135deg, #8000ff, #ff0080);
        }

        .hex-6 {
            top: 30%;
            left: 10%;
            background: linear-gradient(135deg, #ff8000, #ffff00);
        }

        /* 六邊形內容樣式 */
        .hex-content {
            position: relative;
            z-index: 2;
            color: white;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
            animation: contentCounter 20s linear infinite reverse;
        }

        @keyframes contentCounter {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(-360deg); }
        }

        .hex-icon {
            font-size: 3rem;
            margin-bottom: 0.5rem;
            filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.8));
        }

        .hex-title {
            font-size: 1rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .hex-desc {
            font-size: 0.7rem;
            opacity: 0.9;
            line-height: 1.2;
        }



        /* 響應式 */
        @media (max-width: 1200px) {
            .hexagon-layout {
                width: 600px;
                height: 450px;
            }

            .hex-item {
                width: 150px;
                height: 150px;
            }

            .hex-center {
                width: 180px;
                height: 180px;
            }

            .hex-icon {
                font-size: 2rem;
            }

            .hex-title {
                font-size: 0.8rem;
            }

            .hex-desc {
                font-size: 0.6rem;
            }
        }

        @media (max-width: 768px) {
            .hexagon-layout {
                width: 400px;
                height: 300px;
            }

            .hex-item {
                width: 100px;
                height: 100px;
            }

            .hex-center {
                width: 120px;
                height: 120px;
            }

            .hex-icon {
                font-size: 1.5rem;
            }

            .hex-title {
                font-size: 0.6rem;
            }

            .hex-desc {
                display: none;
            }
        }

        /* 載入動畫 */
        .fade-in {
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.6s ease forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="hexagon-layout">
            <!-- 中央六邊形 - 遊戲標題 -->
            <div class="hex-item hex-center">
                <div class="hex-content">
                    <div class="hex-icon">🎮</div>
                    <div class="hex-title">Block Battle</div>
                    <div class="hex-desc">Arena</div>
                </div>
            </div>

            <!-- 六邊形 1 - 快速對戰 -->
            <div class="hex-item hex-1" onclick="startGame()">
                <div class="hex-content">
                    <div class="hex-icon">⚡</div>
                    <div class="hex-title">快速對戰</div>
                    <div class="hex-desc">立即開始</div>
                </div>
            </div>

            <!-- 六邊形 2 - 排位賽 -->
            <div class="hex-item hex-2" onclick="openRanked()">
                <div class="hex-content">
                    <div class="hex-icon">🏆</div>
                    <div class="hex-title">排位賽</div>
                    <div class="hex-desc">競技排名</div>
                </div>
            </div>

            <!-- 六邊形 3 - 自定義房間 -->
            <div class="hex-item hex-3" onclick="createRoom()">
                <div class="hex-content">
                    <div class="hex-icon">🎯</div>
                    <div class="hex-title">自定義</div>
                    <div class="hex-desc">創建房間</div>
                </div>
            </div>

            <!-- 六邊形 4 - 玩家信息 -->
            <div class="hex-item hex-4">
                <div class="hex-content">
                    <div class="hex-icon">👤</div>
                    <div class="hex-title">玩家001</div>
                    <div class="hex-desc">排名 156</div>
                </div>
            </div>

            <!-- 六邊形 5 - 貨幣 -->
            <div class="hex-item hex-5">
                <div class="hex-content">
                    <div class="hex-icon">💰</div>
                    <div class="hex-title">1,250</div>
                    <div class="hex-desc">金幣</div>
                </div>
            </div>

            <!-- 六邊形 6 - 設置 -->
            <div class="hex-item hex-6" onclick="openSettings()">
                <div class="hex-content">
                    <div class="hex-icon">⚙️</div>
                    <div class="hex-title">設置</div>
                    <div class="hex-desc">遊戲選項</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function startGame() {
            window.location.href = 'index.html';
        }

        function openRanked() {
            alert('排位賽功能即將推出！');
        }

        function createRoom() {
            alert('自定義房間功能即將推出！');
        }

        function openSettings() {
            alert('設置功能即將推出！');
        }

        // 載入動畫
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.fade-in');
            elements.forEach((el, index) => {
                el.style.animationDelay = `${index * 0.1}s`;
            });
        });
    </script>
</body>
</html>
